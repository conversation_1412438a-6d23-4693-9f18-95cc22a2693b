* SPICE Subcircuit Demo
* Model: OUTPUT_DIFF_DEMO
* Model Type: Output_diff
* Differential output buffer - LVDS/CML driver

.SUBCKT OUTPUT_DIFF_DEMO PAD_P PAD_N VCC VSS PAD_P_RISE PAD_P_FALL PAD_N_RISE PAD_N_FALL
* PAD_P    - Positive differential pad
* PAD_N    - Negative differential pad
* VCC      - Power supply
* VSS      - Ground
* PAD_P_RISE - Positive rising waveform reference
* PAD_P_FALL - Positive falling waveform reference
* PAD_N_RISE - Negative rising waveform reference
* PAD_N_FALL - Negative falling waveform reference

* Differential input capacitance
C_comp_p PAD_P VSS 2pF
C_comp_n PAD_N VSS 2pF

* Pulldown I-V Characteristic
Gpd_p PAD_P VSS TABLE {V(PAD_P,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)
Gpd_n PAD_N VSS TABLE {V(PAD_N,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)

* Pullup I-V Characteristic
Gpu_p VCC PAD_P TABLE {V(VCC,PAD_P)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)
Gpu_n VCC PAD_N TABLE {V(VCC,PAD_N)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)

* Protection clamps
Gclamp_gnd_p VSS PAD_P TABLE {V(VSS,PAD_P)} = (0.7,0) (0.8,0.01) (0.9,0.1)
Gclamp_gnd_n VSS PAD_N TABLE {V(VSS,PAD_N)} = (0.7,0) (0.8,0.01) (0.9,0.1)
Gclamp_pwr_p PAD_P VCC TABLE {V(PAD_P,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)
Gclamp_pwr_n PAD_N VCC TABLE {V(PAD_N,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)

* Timing waveforms
Vrise_p PAD_P_RISE 0 PWL(0 0V 1ns 2.5V 2ns 5V)
Vfall_p PAD_P_FALL 0 PWL(0 5V 1ns 2.5V 2ns 0V)
Vrise_n PAD_N_RISE 0 PWL(0 5V 1ns 2.5V 2ns 0V)
Vfall_n PAD_N_FALL 0 PWL(0 0V 1ns 2.5V 2ns 5V)

.ENDS
