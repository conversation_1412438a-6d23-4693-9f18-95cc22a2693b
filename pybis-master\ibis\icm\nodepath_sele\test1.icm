|
[Begin Header]
[I<PERSON> Ver] 1.0
[File Name]           test1.icm
[Comment Char] |_char                   | This is a trailing comment
[File_Rev]  Revision One Dot Oh  |(NOTE underscore in keyword)
[Date] January 15, 2003
[Source] Whoever generated this cruft.  Must be me.
[Copyright] (C) The IBIS Consortium, 2003
[Support] http://nowhere.nohow.com/not/a/chance.html
The spec does not limit keyword in any way.  It's just a pile of text.
Oh, by the way, a comment follows this point --> % here it is!
[Redistribution] Yes
[Redistribution Text] Unrestricted Residistribution of this file is permitted.
[End Header]

[Begin ICM Family]         family name goes here    | spaces are not ruled out
[Manufacturer]              ACME exploding traps
[ICM Family Description]   A favorite of Wil-E-Coyote
[ICM Model List]
|   NAME         |   MATING           |  SLEW        |  IMAGE
TwoLineModel1      Mated               100ns        1.jpg
|
| NodeMap1
|      --------- --------+ --------- --------- --------+ --------- ---------    NodeMap5
|  --------- --------+ --------- ---------- --------+ ---------- ---------
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |
|                    |                                 |
|                    |   |                          |  |
|                        |                          |  |
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |  |
|                    |   |                          |
|                    |                               NodeMap3
|                    |   |
|                        |
|                    |   |
|                    |   |
|                    |   |
|  NodeMap4          |   |
|     --------- -----|--+
| --------- ---------+
|                        |
|                    |   |
|                    |   |
|                    |   |
|                    |   |
|                    |   |
|                    |
|                        |
|                    |   |
|                    |   |
|                    |   |
|                    |   |
|                    |   |
|                    |
|                      NodeMap2
|
[Begin ICM Model] TwoLineModel1
  ICM_model_type  S-parameter
|
[ICM Model Description]
[Nodal Path Description]
  Model_nodemap  NodeMap1

  N_section(A1      A2      in1_1_1 in1_1_2  ) Mult=1 sect2_s_2_1
  N_section(in1_1_1 in1_1_2 in1_1   in1_2    ) Mult=1 sect2_s_2_2
 
  N_section(in1_1   in1_2   in2_1_1 in2_1_2  ) Mult=3 sect2_s_2_3
  N_section(in2_1_1 in2_1_2 in2_2_1 in2_2_2  ) Mult=1 sect2_s_2_4
  N_section(in2_2_1 in2_2_2 in2_1   in2_2    ) Mult=1 sect2_s_2_5

  N_section(in2_1   in2_2   D1_1    D1_2     ) Mult=1 sect2_s_2_6
  N_section(D1_1    D1_2    D1      D2       ) Mult=1 sect2_s_2_7

  Model_nodemap  NodeMap4

  N_section(in2_1   in2_2   B1_1    B1_2     ) Mult=1 sect2_s_2_8
  N_section(B1_1    B1_2    B1      B2       ) Mult=1 sect2_s_2_9
  
  Model_nodemap  NodeMap2

  N_section(in1_1   in1_2   in3_1_1 in3_1_2  ) Mult=1 sect2_s_2_10
  N_section(in3_1_1 in3_1_2 in3_2_1 in3_2_2  ) Mult=1 sect2_s_2_11
  N_section(in3_2_1 in3_2_2 in3_1   in3_2    ) Mult=1 sect2_s_2_12

  N_section(in3_1   in3_2   C1_1    C1_2     ) Mult=1 sect2_s_2_13
  N_section(C1_1    C1_2    C1      C2       ) Mult=1 sect2_s_2_14

  Model_nodemap  NodeMap3

  N_section(in3_1   in3_2   E1_1    E1_2     ) Mult=1 sect2_s_2_15
  N_section(E1_1    E1_2    E1      E2       ) Mult=1 sect2_s_2_16

  Model_nodemap  NodeMap5
[End ICM Model]

[ICM Node Map] NodeMap1
|
| Pin Node Sig
  1   A1   Sig1
  2   A2   Sig2
|
[ICM Node Map] NodeMap2
|
| Pin Node Sig
  1   B1   Sig1
  2   B2   Sig2
|
[ICM Node Map] NodeMap3
|
| Pin Node Sig
  1   C1   Sig1
  2   C2   Sig2
|
[ICM Node Map] NodeMap4
|
| Pin Node Sig
  1   D1   Sig1
  2   D2   Sig2
|
[ICM Node Map] NodeMap5
|
| Pin Node Sig
  1   E1   Sig1
  2   E2   Sig2
|
[End ICM Family]
|
[Begin ICM Section] sect2_s_2_1
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   A1
2   A2
3   in1_1_1
4   in1_1_2
|
[End ICM Section] sect2_s_2_1
|
[Begin ICM Section] sect2_s_2_2
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in1_1_1
2   in1_1_2
3   in1_1
4   in1_2
|
[End ICM Section] sect2_s_2_2
|
[Begin ICM Section] sect2_s_2_3
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in1_1
2   in1_2
3   in2_1_1
4   in2_1_2
|
[End ICM Section] sect2_s_2_3
|
[Begin ICM Section] sect2_s_2_4
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in2_1_1
2   in2_1_2
3   in2_2_1
4   in2_2_2
|
[End ICM Section] sect2_s_2_4
|
[Begin ICM Section] sect2_s_2_5
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in2_2_1
2   in2_2_2
3   in2_1
4   in2_2
|
[End ICM Section] sect2_s_2_5
|
[Begin ICM Section] sect2_s_2_6
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in2_1
2   in2_2
3   D1_1
4   D1_2
[End ICM Section] sect2_s_2_6
|
[Begin ICM Section] sect2_s_2_7
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   D1_1
2   D1_2
3   D1
4   D2
[End ICM Section] sect2_s_2_7
|
[Begin ICM Section] sect2_s_2_8
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in2_1
2   in2_2
3   B1_1
4   B1_2
|
[End ICM Section] sect2_s_2_8
|
[Begin ICM Section] sect2_s_2_9
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   B1_1
2   B1_2
3   B1
4   B2
|
[End ICM Section] sect2_s_2_9
|
[Begin ICM Section] sect2_s_2_10
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in1_1
2   in1_2
3   in3_1_1
4   in3_1_2
|
[End ICM Section] sect2_s_2_10
|
[Begin ICM Section] sect2_s_2_11
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in3_1_1
2   in3_1_2
3   in3_2_1
4   in3_2_2
|
[End ICM Section] sect2_s_2_11
|
[Begin ICM Section] sect2_s_2_12
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in3_2_1
2   in3_2_2
3   in3_1
4   in3_2
|
[End ICM Section] sect2_s_2_12
|
[Begin ICM Section] sect2_s_2_13
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in3_1
2   in3_2
3   C1_1
4   C1_2
|
[End ICM Section] sect2_s_2_13
|
[Begin ICM Section] sect2_s_2_14
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   C1_1
2   C1_2
3   C1
4   C2
|
[End ICM Section] sect2_s_2_14
|
[Begin ICM Section] sect2_s_2_15
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   in3_1
2   in3_2
3   E1_1
4   E1_2
|
[End ICM Section] sect2_s_2_15
|
[Begin ICM Section] sect2_s_2_16
[Derivation Method] Lumped
[ICM S-parameter]
File_name s_w_test_GHz_db.s4p
Port_assignment
| Port Node
1   E1_1
2   E1_2
3   E1
4   E2
|
[End ICM Section] sect2_s_2_16
|
[End]
