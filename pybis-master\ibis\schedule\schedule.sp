* [IBIS] Driver Schedule Basic Function
*   SCH_OUT, SCH_IO  are composed of 4 open_drains and 
*     4 open_sources with different delays
*   Prepared by <PERSON><PERSON> Nov, 2001

.option post

*********************** Analysis *********************************************

.tran 0.05n 30n 

*********************** Stimuli **********************************************
.param CLK_FRQ    =  66x   $ 66MHz
.param CLK_PRD    = '1/CLK_FRQ'
.param CLK_H_PRD  = '0.5*CLK_PRD'
.param CLK_Q_PRD  = '0.25*CLK_PRD'
.param DLT_TIME   = 0.05n
.param R_load_value = 75

v_in1 nd_in1  0  pulse ( 0V 1.0V CLK_Q_PRD DLT_TIME DLT_TIME CLK_H_PRD CLK_PRD )

v_in2 nd_in2  0  pulse ( 1.1V 0V CLK_Q_PRD DLT_TIME DLT_TIME CLK_H_PRD CLK_PRD )
v_en2 nd_en2  0  1V

Vpu1 nd_pu1 0 1.5V
Vpd1 nd_pd1 0 0V

*********************** Output Buffer ****************************************
b1 nd_pu1 nd_pd1 nd_out1 nd_in1 $ nd_pc1 nd_gc1
+ file = 'schedule.ibs'
+ model = 'SCH_OUT'
+ typ=typ power=off
+ interpol=2
+ nowarn

*********************** I/O Buffer *******************************************
b2 nd_pu2 nd_pd2 nd_out2 nd_in2 nd_en2 nd_outofin2 $ nd_pc2 nd_gc2
+ file = 'schedule.ibs'
+ model = 'SCH_IO'
+ typ=typ power=on
+ interpol=2
+ nowarn

R_load  nd_1  nd_2   R_load_value

*********************** I/O Buffer *******************************************

.PARAM Z_0=75
.PARAM T_delay=1ns
.PARAM Length=0.1

W1 N=1 nd_out1 GND nd_1 GND Umodel=Uname L=Length
W2 N=1 nd_out2 GND nd_2 GND Umodel=Uname L=Length
.model Uname u nl=1 level=3 elev=3 llev=0 plev=1 nlay=2
+ zk=Z_0 delay=T_delay

.op 
.option post_version = 9601 
.option post = 1
.meas tran vout1 avg v(nd_out1) from=0ns to=10ns
.meas tran vout2 avg v(nd_out2) from=0ns to=10ns
.meas tran v1 avg v(nd_1) from=10ns to=20ns
.meas tran v2 avg v(nd_2) from=10ns to=20ns

.end
