|
[Begin Header]
[<PERSON><PERSON> Ver] 1.0
[File Name]           test1.icm
[Comment Char] |_char                   | This is a trailing comment
[File_Rev]  Revision One Dot Oh  |(NOTE underscore in keyword)
[Date] January 15, 2003
[Source] Whoever generated this cruft.  Must be me.
[Copyright] (C) The IBIS Consortium, 2003
[Support] http://nowhere.nohow.com/not/a/chance.html
The spec does not limit keyword in any way.  It's just a pile of text.
Oh, by the way, a comment follows this point --> % here it is!
[Redistribution] Yes
[Redistribution Text] Unrestricted Residistribution of this file is permitted.
[End Header]

[Begin ICM Family]         family name goes here    | spaces are not ruled out
[Manufacturer]              ACME exploding traps
[ICM Family Description]   A favorite of Wil-E-Coyote
[ICM Model List]
|   NAME         |   MATING           |  SLEW        |  IMAGE
FourLineModel1      Mated               100ns        1.jpg
|
|                              1.2                           1.6
|            sect3_rlgc_4   sect4_w_4     sect3_rlgc_4*2  sect4_w_4        sect3_rlgc_4*2
|  PinMap1  >------------< >----------< >--------------<>---------------< >-----------< PinMap5
|                                      |                                 |
|                                      |                                 |
|                                      |sect4_w_4 0.9                    | sect3_rlgc_4*2
|                                      |                                 |
|                                      ^                                 ^
|                                      \/                              PinMap3
|                                      |
|                                      |
|                                      |sect3_rlgc_4*2
|                              0.5     |
|            sect3_rlgc_4   sect4_w_4  ^
|  PinMap4   >------------<>-----------
|                                      \/
|                                      |
|                                      |  sect3_rlgc_4
|                                      |
|                                      |
|                                      ^
|                                      \/
|                                      |
|                                      |  sect4_w_4 0.35
|                                      |
|                                      |
|                                      ^
|                                  PinMap2
|
[Begin ICM Model] FourLineModel1
  ICM_model_type  MLM
  SGR             1:3
  Ref_impedance  =  75
|
[ICM Model Description]
[Tree Path Description]
  Model_pinmap  PinMap1
  Section       Mult= 1    sect3_rlgc_4
  Section       Len = 1.2  sect4_w_4  
  Fork
    Section       Len = 0.9  sect4_w_4
    Section       Mult = 2  sect3_rlgc_4  
    Fork
      Section       Len= 0.5  sect4_w_4
      Section       Mult = 1  sect3_rlgc_4
      Model_pinmap    PinMap4
    Endfork
    Section      Mult = 1   sect3_rlgc_4
    Section      Len = 0.35   sect4_w_4
    Model_pinmap  PinMap2
  Endfork
  Section       Mult = 2     sect3_rlgc_4
  Section       Len = 1.6    sect4_w_4
  Fork
    Section    Mult = 1   sect3_rlgc_4
    Section    Mult = 1   sect3_rlgc_4
    Model_pinmap PinMap3
  Endfork
  Section      Mult = 2  sect3_rlgc_4
  Model_pinmap  PinMap5
[End ICM Model]

[ICM Pin Map] PinMap1
  Pin_order  Column_ordered
  Num_of_rows  =  1
  Num_of_columns = 4
  Pin_list
|
  1  sig1
  2  sig2
  3  sig3
  4  sig4
|
[ICM Pin Map] PinMap2
  Pin_order  Column_ordered
  Num_of_rows  =  1
  Num_of_columns = 4
  Pin_list
|
  1  sig1
  2  sig2
  3  sig3
  4  sig4
|
[ICM Pin Map] PinMap3
  Pin_order  Column_ordered
  Num_of_rows  =  1
  Num_of_columns = 4
  Pin_list
|
  1  sig1
  2  sig2
  3  sig3
  4  sig4
|
[ICM Pin Map] PinMap4
  Pin_order  Column_ordered
  Num_of_rows  =  1
  Num_of_columns = 4
  Pin_list
|
  1  sig1
  2  sig2
  3  sig3
  4  sig4
|
[ICM Pin Map] PinMap5
  Pin_order  Column_ordered
  Num_of_rows  =  1
  Num_of_columns = 4
  Pin_list
|
  1  sig1
  2  sig2
  3  sig3
  4  sig4
|
[End ICM Family]


[Begin ICM Section] sect3_rlgc_4
[Derivation Method] Lumped
|
[Resistance Matrix] Diagonal_matrix
|[Row] 1
|0.357348   0.0153167 0.000203431 0.0
0.357348
|[Row] 2
|0.360486  0.00520121 0.000603478
0.376849
|[Row] 3
|0.358009   0.0157098
0.45466
|[Row] 4
0.356194
|
[Inductance Matrix] Full_matrix
[Row] 1
3.12277e-009     1.01577e-009     3.05886e-010     2.22836e-010
[Row] 2
3.02065e-009     4.47214e-010     2.93897e-010
[Row] 3
2.99636e-009     9.45824e-010
[Row] 4
3.01787e-009
|
[Capacitance Matrix] Full_matrix
[Row] 1
4.03601e-013    -1.28769e-013    -1.50353e-014    -8.75617e-015
[Row] 2
4.19554e-013    -3.78802e-014    -1.42581e-014
[Row] 3
4.16062e-013    -1.22606e-013
[Row] 4
4.06634e-013
|
[Conductance Matrix] Diagonal_matrix
0
0
0
0
|
[End ICM Section] sect3_rlgc_4
|
[Begin ICM Section] sect4_w_4
[Derivation Method] Distributed
|
[Resistance Matrix] Diagonal_matrix
|[Row] 1
|0.357348   0.0153167 0.000203431 0.0
0.357348
|[Row] 2
|0.360486  0.00520121 0.000603478
0.376849
|[Row] 3
|0.358009   0.0157098
0.45466
|[Row] 4
0.356194
|
[Inductance Matrix] Full_matrix
[Row] 1
3.12277e-009     1.01577e-009     3.05886e-010     2.22836e-010
[Row] 2
3.02065e-009     4.47214e-010     2.93897e-010
[Row] 3
2.99636e-009     9.45824e-010
[Row] 4
3.01787e-009
|
[Capacitance Matrix] Full_matrix
[Row] 1
4.03601e-013    -1.28769e-013    -1.50353e-014    -8.75617e-015
[Row] 2
4.19554e-013    -3.78802e-014    -1.42581e-014
[Row] 3
4.16062e-013    -1.22606e-013
[Row] 4
4.06634e-013
|
[Conductance Matrix] Diagonal_matrix
0
0
0
0
|
[End ICM Section] sect4_w_4

[End]
