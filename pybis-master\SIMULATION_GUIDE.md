# 仿真测试指南

本指南说明如何使用提供的测试文件来验证IBIS到SPICE转换的准确性。

## 文件说明

### 测试文件
- `test_ibis_hspice.sp` - 使用原始IBIS模型的HSPICE测试文件
- `test_spice_ngspice.cir` - 使用转换后SPICE子电路的NGSPICE测试文件
- `compare_simulation_results.py` - 结果对比分析脚本

### 模型文件
- `ibis/at16245.ibs` - 原始IBIS模型文件
- `AT16245_OUTPUT.sp` - 转换后的SPICE子电路

## 仿真步骤

### 1. HSPICE仿真（原始IBIS模型）

```bash
# 运行HSPICE仿真
hspice test_ibis_hspice.sp

# 查看结果
cat test_ibis_hspice.lis
```

**预期输出文件：**
- `test_ibis_hspice.lis` - 仿真日志和测量结果
- `test_ibis_hspice.tr0` - 瞬态分析波形数据
- `test_ibis_hspice.ac0` - 交流分析数据

### 2. NGSPICE仿真（转换后SPICE模型）

```bash
# 运行NGSPICE仿真
ngspice test_spice_ngspice.cir

# 或者交互式运行
ngspice
ngspice 1 -> source test_spice_ngspice.cir
ngspice 2 -> run
ngspice 3 -> plot v(PAD) v(PAD_RISE) v(PAD_FALL)
ngspice 4 -> quit
```

**预期输出文件：**
- `test_spice_results.raw` - 波形数据
- `temp_results.csv` - 温度特性数据
- 控制台输出的测量结果

### 3. 结果对比分析

```bash
# 运行对比脚本
python compare_simulation_results.py
```

**生成文件：**
- `simulation_comparison.csv` - 详细对比数据
- `simulation_comparison.png` - 对比图表

## 测试参数

### 仿真条件
- **电源电压**: VDD = 5.0V, VSS = 0V
- **温度范围**: 0°C, 25°C, 85°C
- **负载条件**: 50Ω传输线 + 5pF负载电容
- **输入信号**: 脉冲信号，上升/下降时间 0.5ns

### 测量参数
1. **时序参数**
   - `trise` - 上升时间 (10%-90%)
   - `tfall` - 下降时间 (90%-10%)
   - `tphl` - 高到低传播延迟
   - `tplh` - 低到高传播延迟

2. **电压参数**
   - `VOH` - 输出高电平
   - `VOL` - 输出低电平

3. **电流参数**
   - `IOH` - 输出高电平电流
   - `IOL` - 输出低电平电流

4. **信号完整性**
   - 过冲/下冲
   - 建立时间
   - 功耗

## 预期结果

### 良好转换的指标
- **时序误差** < 10%
- **电压误差** < 5%
- **波形形状** 基本一致
- **频率响应** 相似

### 典型测量值
```
Parameter    IBIS Model      SPICE Model     Difference      Error %
trise        1.200ns         1.150ns         -0.050ns        4.17
tfall        0.800ns         0.850ns         +0.050ns        6.25
tphl         2.100ns         2.050ns         -0.050ns        2.38
tplh         1.900ns         1.950ns         +0.050ns        2.63
voh          4.800V          4.750V          -0.050V         1.04
vol          0.200V          0.250V          +0.050V         25.00
```

## 故障排除

### 常见问题

1. **HSPICE找不到IBIS文件**
   ```
   解决方案：确保IBIS文件路径正确，使用绝对路径
   ```

2. **NGSPICE语法错误**
   ```
   解决方案：检查.cir文件中的单位格式（使用n而不是ns）
   ```

3. **测量结果为空**
   ```
   解决方案：检查信号名称和测量条件是否正确
   ```

### 调试技巧

1. **查看波形**
   ```bash
   # HSPICE
   cosmosscope test_ibis_hspice.tr0
   
   # NGSPICE
   ngspice -b test_spice_ngspice.cir
   ```

2. **检查收敛性**
   ```
   增加.option reltol=1e-6 abstol=1e-12
   ```

3. **简化电路**
   ```
   移除寄生元件，使用理想负载进行初步验证
   ```

## 高级分析

### 蒙特卡洛分析
```spice
.mc 100 tran 0.01ns 25ns
.param r_var=agauss(50,5,3)
.param c_var=agauss(5p,0.5p,3)
```

### 工艺角分析
```spice
.lib 'process.lib' TT
.alter
.lib 'process.lib' FF
.alter
.lib 'process.lib' SS
```

### 眼图分析
```spice
.tran 0.01ns 100ns
.probe v(PAD)
* 后处理生成眼图
```

## 结果验证标准

### 通过标准
- 所有时序参数误差 < 15%
- 电压电平误差 < 10%
- 波形相关性 > 0.95
- 无明显振荡或不稳定

### 优秀标准
- 所有参数误差 < 5%
- 波形几乎重合
- 频率响应一致

## 报告模板

```
IBIS to SPICE Conversion Validation Report
==========================================

Model: AT16245_OUT
Date: [日期]
Engineer: [姓名]

Summary:
- Average Error: X.XX%
- Maximum Error: X.XX%
- Overall Assessment: [Excellent/Good/Acceptable/Poor]

Detailed Results:
[插入对比表格]

Recommendations:
[改进建议]
```
