# IBIS Model Type Demos

This directory contains SPICE subcircuit demonstrations for various IBIS model types.

## Generated Files

### INPUT_DEMO.sp
- **Type**: Input
- **Description**: Input buffer - receives signals only
- **Features**: Enable=False, Waveforms=False, Pullup/Pulldown=False, Differential=False

### OUTPUT_DEMO.sp
- **Type**: Output
- **Description**: Output buffer - drives signals with push-pull output
- **Features**: Enable=False, Waveforms=True, Pullup/Pulldown=True, Differential=False

### TRISTATE_DEMO.sp
- **Type**: 3-state
- **Description**: Three-state buffer - output can be disabled (Hi-Z)
- **Features**: Enable=True, Waveforms=True, Pullup/Pulldown=True, Differential=False

### IO_DEMO.sp
- **Type**: I/O
- **Description**: Bidirectional I/O buffer - can both receive and drive
- **Features**: Enable=True, Waveforms=True, Pullup/Pulldown=True, Differential=False

### OPENDRAIN_DEMO.sp
- **Type**: Open_drain
- **Description**: Open drain output - can only pull low
- **Features**: Enable=False, Waveforms=True, Pullup/Pulldown=False, Differential=False

### IO_OPENDRAIN_DEMO.sp
- **Type**: I/O_open_drain
- **Description**: Bidirectional open drain I/O
- **Features**: Enable=True, Waveforms=True, Pullup/Pulldown=False, Differential=False

### INPUT_DIFF_DEMO.sp
- **Type**: Input_diff
- **Description**: Differential input buffer - LVDS/CML receiver
- **Features**: Enable=False, Waveforms=False, Pullup/Pulldown=False, Differential=True

### OUTPUT_DIFF_DEMO.sp
- **Type**: Output_diff
- **Description**: Differential output buffer - LVDS/CML driver
- **Features**: Enable=False, Waveforms=True, Pullup/Pulldown=True, Differential=True

### TRISTATE_DIFF_DEMO.sp
- **Type**: 3-state_diff
- **Description**: Three-state differential buffer
- **Features**: Enable=True, Waveforms=True, Pullup/Pulldown=True, Differential=True

### SERIES_DEMO.sp
- **Type**: Series
- **Description**: Series termination buffer - source termination
- **Features**: Enable=False, Waveforms=True, Pullup/Pulldown=True, Differential=False

### TERMINATOR_DEMO.sp
- **Type**: Terminator
- **Description**: Passive termination element
- **Features**: Enable=False, Waveforms=False, Pullup/Pulldown=False, Differential=False

## Usage

Include these subcircuits in your SPICE simulations:

```spice
.include INPUT_DEMO.sp
X1 signal vcc vss INPUT_DEMO
```

Each subcircuit demonstrates the typical structure and components for that model type.
