* SPICE Subcircuit generated from IBIS model
* Model: TOP_MODEL_BUS_HOLD
* Generated by ibis_to_spice_converter.py - Original Version

.SUBCKT TOP_MODEL_BUS_HOLD PAD VCC VSS ENABLE PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* ENABLE   - Enable signal (for 3-state models)
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output Capacitance: 4.000000e-12F
C_comp PAD VSS 4.000000e-12F

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} =
 (-2 6.158000e+17)
 (-1.9 1.697000e+16)
 (-1.8 4.679000e+14)

+
 (-1.7 1.290000e+13)
 (-1.6 3.556000e+11)
 (-1.5 9.802000e+09)

+
 (-1.4 2.702000e+08)
 (-1.3 7.449000e+06)
 (-1.2 2.053000e+05)

+
 (-1.1 5.660000e+03)
 (-1 156)
 (-0.9 4.308)

+
 (-0.8 0.1221)
 (-0.7 0.004315)
 (-0.6 1.715000e-04)

+
 (-0.5 4.959000e-06)
 (-0.4 1.373000e-07)
 (-0.3 4.075000e-09)

+
 (-0.2 3.044000e-10)
 (-0.1 1.030000e-10)
 (0 0)

+
 (5 0)

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} =
 (-2 -6.158000e+17)
 (-1.9 -1.697000e+16)
 (-1.8 -4.679000e+14)

+
 (-1.7 -1.290000e+13)
 (-1.6 -3.556000e+11)
 (-1.5 -9.802000e+09)

+
 (-1.4 -2.702000e+08)
 (-1.3 -7.449000e+06)
 (-1.2 -2.053000e+05)

+
 (-1.1 -5.660000e+03)
 (-1 -156)
 (-0.9 -4.308)

+
 (-0.8 -0.1221)
 (-0.7 -0.004315)
 (-0.6 -1.715000e-04)

+
 (-0.5 -4.959000e-06)
 (-0.4 -1.373000e-07)
 (-0.3 -4.075000e-09)

+
 (-0.2 -3.044000e-10)
 (-0.1 -1.030000e-10)
 (0 0)

+
 (5 0)

.ENDS
