* HSPICE test circuit using original IBIS model
* AT16245_OUT model simulation
* Comparison test for IBIS vs SPICE subcircuit

.TITLE AT16245_OUT IBIS Model Test - HSPICE

* Include IBIS model (HSPICE format)
.LIB 'ibis/at16245.ibs'

* Supply voltages
VDD VDD 0 DC 5.0V
VSS VSS 0 DC 0V

* Input stimulus for timing analysis
VIN_DATA DATA 0 PULSE(0V 5V 2ns 0.5ns 0.5ns 8ns 20ns)
VIN_ENABLE ENABLE 0 PULSE(0V 5V 1ns 0.1ns 0.1ns 18ns 20ns)

* Load network (typical PCB trace + receiver)
* Transmission line model
TLINE PAD PAD_RCV Z0=50 TD=1ns

* Receiver load
CL_RCV PAD_RCV 0 5pF
RL_RCV PAD_RCV 0 1MEG

* Additional parasitic elements
* Package parasitics
LPKG VDD VDD_PKG 2nH
CPKG VDD_PKG 0 1pF
RPKG VDD VDD_PKG 0.1

LPKG_GND VSS_PKG VSS 2nH
CPKG_GND VSS_PKG 0 1pF
RPKG_GND VSS_PKG VSS 0.1

* PCB trace parasitics
LTRACE PAD PAD_TRACE 5nH
CTRACE PAD_TRACE 0 2pF

* IBIS buffer instance (using IBIS-to-SPICE interface)
* Note: Actual IBIS integration syntax may vary by HSPICE version
XBUF PAD VDD_PKG VSS_PKG DATA ENABLE AT16245_OUT

* Analysis setup
.OPTION POST=2 PROBE ACCURATE RUNLVL=6
.OPTION RELTOL=1e-6 ABSTOL=1e-12 VNTOL=1e-6

* Transient analysis
.TRAN 0.01ns 25ns SWEEP TEMP 0 85 25

* Measurements
* Rise time (10% to 90%)
.MEASURE TRAN TRISE TRIG V(PAD) VAL=0.5V RISE=1 TARG V(PAD) VAL=4.5V RISE=1

* Fall time (90% to 10%)
.MEASURE TRAN TFALL TRIG V(PAD) VAL=4.5V FALL=1 TARG V(PAD) VAL=0.5V FALL=1

* Propagation delays
.MEASURE TRAN TPHL TRIG V(DATA) VAL=2.5V FALL=1 TARG V(PAD) VAL=2.5V FALL=1
.MEASURE TRAN TPLH TRIG V(DATA) VAL=2.5V RISE=1 TARG V(PAD) VAL=2.5V RISE=1

* Output voltage levels
.MEASURE TRAN VOH MAX V(PAD) FROM=10ns TO=15ns
.MEASURE TRAN VOL MIN V(PAD) FROM=15ns TO=20ns

* Current measurements
.MEASURE TRAN IOH MAX I(VDD) FROM=10ns TO=15ns
.MEASURE TRAN IOL MAX I(VSS) FROM=15ns TO=20ns

* Power consumption
.MEASURE TRAN PWR_AVG AVG POWER FROM=2ns TO=22ns

* Signal integrity checks
* Overshoot measurement
.MEASURE TRAN OVERSHOOT MAX V(PAD) FROM=2ns TO=6ns
.MEASURE TRAN UNDERSHOOT MIN V(PAD) FROM=12ns TO=16ns

* Settling time
.MEASURE TRAN TSETTLE TRIG V(PAD) VAL=4.5V RISE=1 TARG V(PAD) VAL=4.75V CROSS=LAST

* Output impedance analysis
.AC DEC 100 1MEG 1GHZ

* Probe statements for waveform output
.PROBE V(DATA) V(PAD) V(PAD_RCV) V(ENABLE)
.PROBE I(VDD) I(VSS) I(XBUF.PAD)

* Print key results
.PRINT TRAN V(DATA) V(PAD) V(PAD_RCV) I(VDD) I(VSS)
.PRINT AC VDB(PAD) VP(PAD)

* Temperature sweep for characterization
.TEMP 0 25 85

* Process corners (if available)
.ALTER
.LIB 'ibis/at16245.ibs' AT16245_OUT FAST
.TRAN 0.01ns 25ns
.ALTER
.LIB 'ibis/at16245.ibs' AT16245_OUT SLOW
.TRAN 0.01ns 25ns

.END
