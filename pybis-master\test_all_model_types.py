#!/usr/bin/env python3
"""
测试所有IBIS模型类型的转换支持
验证转换器对不同模型类型的正确处理
"""

from ibis_to_spice_converter_v4 import determine_model_features, get_model_type_description

def test_model_type_support():
    """测试所有支持的IBIS模型类型"""
    
    # 定义所有IBIS模型类型
    all_model_types = [
        'Input', 'Output', 'I/O', '3-state',
        'Open_drain', 'I/O_open_drain', 
        'Open_sink', 'I/O_open_sink',
        'Open_source', 'I/O_open_source',
        'Input_ECL', 'Output_ECL', 'I/O_ECL', '3-state_ECL',
        'Input_diff', 'Output_diff', 'I/O_diff', '3-state_diff',
        'Series', 'Series_switch', 'Terminator'
    ]
    
    print("=" * 80)
    print("IBIS Model Type Support Test")
    print("=" * 80)
    print()
    
    # 表头
    print(f"{'Model Type':<20} {'Enable':<8} {'Waveforms':<10} {'Pull U/D':<10} {'Diff':<6} {'Description'}")
    print("-" * 80)
    
    # 测试每种模型类型
    for model_type in all_model_types:
        try:
            # 获取功能特性
            enable, waveforms, pullup_pulldown, differential = determine_model_features(model_type)
            
            # 获取描述
            description = get_model_type_description(model_type)
            
            # 格式化输出
            enable_str = "✅" if enable else "❌"
            waveforms_str = "✅" if waveforms else "❌"
            pullup_str = "✅" if pullup_pulldown else "❌"
            diff_str = "✅" if differential else "❌"
            
            # 截断描述以适应表格
            desc_short = description[:35] + "..." if len(description) > 35 else description
            
            print(f"{model_type:<20} {enable_str:<8} {waveforms_str:<10} {pullup_str:<10} {diff_str:<6} {desc_short}")
            
        except Exception as e:
            print(f"{model_type:<20} ERROR: {e}")
    
    print()
    print("Legend:")
    print("  Enable: Needs ENABLE signal for 3-state control")
    print("  Waveforms: Needs PAD_RISE/PAD_FALL waveform outputs")
    print("  Pull U/D: Needs Pullup/Pulldown I-V characteristics")
    print("  Diff: Differential signal type (needs PAD_P/PAD_N)")

def test_port_generation():
    """测试不同模型类型的端口生成"""
    
    print("\n" + "=" * 80)
    print("Port Generation Test")
    print("=" * 80)
    
    test_cases = [
        ('Input', 'Basic input buffer'),
        ('Output', 'Standard output buffer'),
        ('3-state', 'Three-state buffer'),
        ('I/O', 'Bidirectional I/O'),
        ('Open_drain', 'Open drain output'),
        ('I/O_open_drain', 'Bidirectional open drain'),
        ('Input_diff', 'Differential input'),
        ('Output_diff', 'Differential output'),
        ('3-state_diff', 'Differential 3-state'),
        ('Series', 'Series termination'),
        ('Terminator', 'Passive terminator')
    ]
    
    for model_type, description in test_cases:
        print(f"\n{model_type} ({description}):")
        
        # 获取功能特性
        enable, waveforms, pullup_pulldown, differential = determine_model_features(model_type)
        
        # 生成端口列表
        if differential:
            ports = ["PAD_P", "PAD_N", "VCC", "VSS"]
        else:
            ports = ["PAD", "VCC", "VSS"]
        
        if enable:
            ports.append("ENABLE")
        
        if waveforms:
            if differential:
                ports.extend(["PAD_P_RISE", "PAD_P_FALL", "PAD_N_RISE", "PAD_N_FALL"])
            else:
                ports.extend(["PAD_RISE", "PAD_FALL"])
        
        print(f"  Ports: {' '.join(ports)}")
        print(f"  Count: {len(ports)} ports")

def test_feature_combinations():
    """测试功能组合的逻辑正确性"""
    
    print("\n" + "=" * 80)
    print("Feature Combination Logic Test")
    print("=" * 80)
    
    # 测试用例：(模型类型, 预期特性)
    test_cases = [
        ('Input', (False, False, False, False)),           # 只接收
        ('Output', (False, True, True, False)),            # 推挽输出
        ('3-state', (True, True, True, False)),            # 三态控制
        ('I/O', (True, True, True, False)),                # 双向
        ('Open_drain', (False, True, False, False)),       # 开漏
        ('I/O_open_drain', (True, True, False, False)),    # 双向开漏
        ('Input_diff', (False, False, False, True)),       # 差分输入
        ('Output_diff', (False, True, True, True)),        # 差分输出
        ('3-state_diff', (True, True, True, True)),        # 差分三态
        ('Series', (False, True, True, False)),            # 串联端接
        ('Terminator', (False, False, False, False)),      # 无源端接
    ]
    
    all_passed = True
    
    for model_type, expected in test_cases:
        actual = determine_model_features(model_type)
        
        if actual == expected:
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
            all_passed = False
        
        print(f"{model_type:<15}: Expected {expected}, Got {actual} {status}")
    
    print(f"\nOverall result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")

def generate_model_type_summary():
    """生成模型类型支持总结"""
    
    print("\n" + "=" * 80)
    print("Model Type Support Summary")
    print("=" * 80)
    
    categories = {
        'Basic Types': ['Input', 'Output', 'I/O', '3-state'],
        'Open Drain/Source': ['Open_drain', 'I/O_open_drain', 'Open_sink', 'I/O_open_sink', 'Open_source', 'I/O_open_source'],
        'ECL Logic': ['Input_ECL', 'Output_ECL', 'I/O_ECL', '3-state_ECL'],
        'Differential': ['Input_diff', 'Output_diff', 'I/O_diff', '3-state_diff'],
        'Termination': ['Series', 'Series_switch', 'Terminator']
    }
    
    total_supported = 0
    
    for category, types in categories.items():
        print(f"\n{category}:")
        for model_type in types:
            description = get_model_type_description(model_type)
            print(f"  ✅ {model_type:<20} - {description}")
            total_supported += 1
    
    print(f"\nTotal supported model types: {total_supported}")
    
    # 使用建议
    print("\n" + "=" * 80)
    print("Usage Recommendations")
    print("=" * 80)
    print("""
1. Input models: Use for receivers, comparators, clock inputs
2. Output models: Use for standard logic gates, clock drivers
3. 3-state models: Use for bus drivers, memory interfaces
4. I/O models: Use for bidirectional pins, GPIO
5. Open drain: Use for I2C, 1-wire, wired-OR buses
6. Differential: Use for LVDS, CML, high-speed serial
7. ECL: Use for high-speed ECL logic families
8. Series: Use for source-terminated transmission lines
9. Terminator: Use for passive termination elements
    """)

def main():
    """主测试函数"""
    test_model_type_support()
    test_port_generation()
    test_feature_combinations()
    generate_model_type_summary()

if __name__ == "__main__":
    main()
