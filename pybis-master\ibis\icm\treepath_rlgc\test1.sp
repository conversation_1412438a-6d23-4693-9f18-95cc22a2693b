*
**************** test for ICM [Tree Path Description] ************
******************************************************************
*                              1.2                           1.6                       
*            sect3_rlgc_4   sect4_w_4     sect3_rlgc_4*2  sect4_w_4        sect3_rlgc_4*2
*  PinMap1  >------------< >----------< >--------------<>---------------< >-----------< PinMap5
*                                      |                                 |
*                                      |                                 |
*                                      |sect4_w_4  0.9                   | sect3_rlgc_4*2
*                                      |                                 |
*                                      ^                                 ^
*                                      \/                              PinMap3
*                                      |
*                                      |
*                                      |sect3_rlgc_4*2                  
*                            0.5       | 
*            sect3_rlgc_4   sect4_w_4  ^                                 
*  PinMap4   >------------<>-----------                                
*                                      \/
*                                      |
*                                      |  sect3_rlgc_4
*                                      |
*                                      |
*                                      ^
*                                      \/
*                                      |
*                                      |  sect4_w_4 0.35
*                                      |
*                                      |
*                                      ^
*                                  PinMap2
*
***************** ICM model **************************************
.icm icm1
+ file='test1.icm"
+ model='FourLineModel1'
+ swath = 1

Vicm1_in1 icm1_PinMap1_1 0 ac=1.5 pwl(0ns 5v 2ns 0v 5ns 3v 7ns 4v 10ns 0v)
Ricm1_2_1 icm1_PinMap2_1 0 50
Ricm1_2_2 icm1_PinMap2_2 0 50
Ricm1_2_3 icm1_PinMap2_3 0 50
Ricm1_2_4 icm1_PinMap2_4 0 50
Ricm1_3_1 icm1_PinMap3_1 0 50
Ricm1_3_2 icm1_PinMap3_2 0 50
Ricm1_3_3 icm1_PinMap3_3 0 50
Ricm1_3_4 icm1_PinMap3_4 0 50
Ricm1_4_1 icm1_PinMap4_1 0 50
Ricm1_4_2 icm1_PinMap4_2 0 50
Ricm1_4_3 icm1_PinMap4_3 0 50
Ricm1_4_4 icm1_PinMap4_4 0 50
Ricm1_5_1 icm1_PinMap5_1 0 50
Ricm1_5_2 icm1_PinMap5_2 0 50
Ricm1_5_3 icm1_PinMap5_3 0 50
Ricm1_5_4 icm1_PinMap5_4 0 50

***************** r l g c element *************************************
.include 'sect3_rlgc_4.inc'
.include 'sect4_w_4.inc'

x1_1 pin1_1     pin1_2     pin1_3     pin1_4     inter1_1_1 inter1_1_2 inter1_1_3 inter1_1_4 sect3_rlgc_4
W1_2 N=4 inter1_1_1 inter1_1_2 inter1_1_3 inter1_1_4 0 inter1_1 inter1_2 inter1_3 inter1_4 0 RLGCMODEL=sect4_w_4 L=1.2

x2_1 inter1_1   inter1_2   inter1_3   inter1_4   inter2_1_1 inter2_1_2 inter2_1_3 inter2_1_4 sect3_rlgc_4
x2_2 inter2_1_1 inter2_1_2 inter2_1_3 inter2_1_4 inter2_2_1 inter2_2_2 inter2_2_3 inter2_2_4 sect3_rlgc_4
W2_3 N=4 inter2_2_1 inter2_2_2 inter2_2_3 inter2_2_4 0 inter2_1  inter2_2  inter2_3  inter2_4 0 RLGCMODEL=sect4_w_4 L=1.6

x3_1 inter2_1   inter2_2   inter2_3   inter2_4   pin5_1_1   pin5_1_2   pin5_1_3   pin5_1_4   sect3_rlgc_4
x3_2 pin5_1_1   pin5_1_2   pin5_1_3   pin5_1_4   pin5_1     pin5_2     pin5_3     pin5_4     sect3_rlgc_4

x4_1 inter2_1   inter2_2   inter2_3   inter2_4   pin3_1_1   pin3_1_2   pin3_1_3   pin3_1_4   sect3_rlgc_4
x4_2 pin3_1_1   pin3_1_2   pin3_1_3   pin3_1_4   pin3_1     pin3_2     pin3_3     pin3_4     sect3_rlgc_4

W5_1 N=4 inter1_1 inter1_2 inter1_3 inter1_4 0 inter3_1_1 inter3_1_2 inter3_1_3 inter3_1_4 0 RLGCMODEL=sect4_w_4 L=0.9
x5_2 inter3_1_1 inter3_1_2 inter3_1_3 inter3_1_4 inter3_2_1 inter3_2_2 inter3_2_3 inter3_2_4 sect3_rlgc_4
x5_3 inter3_2_1 inter3_2_2 inter3_2_3 inter3_2_4 inter3_1   inter3_2   inter3_3   inter3_4   sect3_rlgc_4

x6_1 inter3_1   inter3_2   inter3_3   inter3_4   pin2_2_1   pin2_2_2   pin2_2_3   pin2_2_4   sect3_rlgc_4
W6_2 N=4 pin2_2_1 pin2_2_2 pin2_2_3 pin2_2_4 0 pin2_1 pin2_2 pin2_3 pin2_4 0 RLGCMODEL=sect4_w_4 L=0.35

W7_1 N=4 inter3_1 inter3_2 inter3_3  nter3_4 0 pin4_1_1 pin4_1_2 pin4_1_3 pin4_1_4 0 RLGCMODEL=sect4_w_4 L=0.5
x7_2 pin4_1_1   pin4_1_2   pin4_1_3   pin4_1_4   pin4_1     pin4_2     pin4_3     pin4_4     sect3_rlgc_4

Vrlgc_in1 pin1_1 0 ac=1.5 pwl(0ns 5v 2ns 0v 5ns 3v 7ns 4v 10ns 0v)
Rrlgc_2_1 pin2_1 0 50
Rrlgc_2_2 pin2_2 0 50
Rrlgc_2_3 pin2_3 0 50
Rrlgc_2_4 pin2_4 0 50
Rrlgc_3_1 pin3_1 0 50
Rrlgc_3_2 pin3_2 0 50
Rrlgc_3_3 pin3_3 0 50
Rrlgc_3_4 pin3_4 0 50
Rrlgc_4_1 pin4_1 0 50
Rrlgc_4_2 pin4_2 0 50
Rrlgc_4_3 pin4_3 0 50
Rrlgc_4_4 pin4_4 0 50
Rrlgc_5_1 pin5_1 0 50
Rrlgc_5_2 pin5_2 0 50
Rrlgc_5_3 pin5_3 0 50
Rrlgc_5_4 pin5_4 0 50

.option post
.tran 0.1ns 1ns
.ac lin 1k 10k 12k 
.end
