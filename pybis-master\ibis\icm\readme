nodepath_rlgc  ----- node path description with rlgc section
nodepath_selem ----- node path description with s parameter section
treepath_rlgc  ----- tree path description with rlgc section
treepath_swath ----- tree path description with rlgc section and swath matrix expanding

note,
1. basic syntax for icm model call in HSPICE
   .icm icm_name
   + file  = "icmfilename.icm"
   + model = "icmmodelname"
   + swath                   * indicate using HSPICE swath matrix expanding feature

2. tree path description does not support s parameter model.

3. node path description does not support swath matrix expanding feature.

4. external pin naming convention
   "icm_name" + "_" + "pin/node map name"+"_"+"pin name"
   Where "icm_name" is defined by ".icm" statement, and "pin name" is
   defined within [ICM Pin Map] and [ICM Node Map] keywords in ICM file.
   All pin names of a ICM model will be automatically created internally
   by HSPICE, so users need not to spend time to create more nodes for
   ICM model, they just use these node name defined by HSPICE internally
