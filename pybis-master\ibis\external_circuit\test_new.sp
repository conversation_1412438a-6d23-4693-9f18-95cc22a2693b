**********************************************************************
* IBIS4.1 features test : just test connection of buffers in component
*                         - [External Model] and [External Circuit] 
* Component with package=1
* 2006.10.13 
**********************************************************************
*
*
*
*    ---------------------------+       
*                               |       
*    ----------+----+-----------|---------- Pin1
*              |    |           |       
*              |    |           |       
*            |\|    |           |       
*            | \    |           |       
*    --------|  +---|-----------|---------- Pin3
*            | /    |           |       
*            |/|    |           |       	
*              |    |           |       
*       +------+    |           |        
*       |           |           |       
*       |      +----+           |       
*       |      |                |       
*       |    |\|                |       
*       |    | \                |       
*    ---|----|  +---------------|---------- Pin4
*       |    | /                |       
*       |    |/|                |       
*       |      |                |       
*       |      |                |       
*    ---+------+----------------|---------- Pin2 
*                               |       
*    ---------------------------+       
*
*
*
**********************************************************************
.tran 0.01ns 50.0ns
.options post 
.temp=50

**********************************************************************
.include 'IO_buf1.inc'
**********************************************************************


**********************************************************************
* A Circirt Equivalent to .IBIS Component
**********************************************************************
*
**********************************************************************
* IBIS Buffer no Involving External Spice Model
**********************************************************************
vpls_ibis1   in_b1   0  PWL (
+ 0n 5.0
+ 0.25n 5.0
+ 0.35n 0.0
+ 25.35n 0.0
+ 25.45n 5.0
+ 55n 5.0)

ven_ibis1    en_b1   0 1.0
*
bibis   pu_bus1_o pd_bus1_o out_b1_o in_b1 en_b1 oti_b1 pu_bus1_o pd_bus1_o
+ file=  'test_new.ibs'
+ model= 'btest'
+ ramp_rwf=2
+ ramp_fwf=2
+ power=off
*--------------------------------------------------------------------*
* bibis Package, Load and power supply bus 
*--------------------------------------------------------------------*
*------------ Package------------------------------------------------*
rpkg1  out_b1_o pkg1 50.000mOhm
lpkg1  pkg1 out_b1 5.00nH
cpkg1  out_b1 0 2.00pF
*-------------Load---------------------------------------------------*
rload1     out_b1     out_b1_v   50
vload1     out_b1_v   0          5.0
*------------No Package for Power supply Bus-----------------------------*
vppsb pu_bus1_o 0 5.0
vgpsb pd_bus1_o 0 0.0
*------------ Package for Power supply Bus-------------------------------*
*rpkgp  pu_bus1_o pkgp 50.000mOhm
*lpkgp  pkgp pu_bus1 5.00nH
*cpkgp  pu_bus1 0 2.00pF
*vppsb pu_bus1     0          5.0v
*
*rpkgg  pd_bus1_o pkgg 50.000mOhm
*lpkgg  pkgg pd_bus1 5.00nH
*cpkgg  pd_bus1 0 2.00pF
*vgpsb pd_bus1    0    0.0v
*


**********************************************************************
* IBIS Buffer involving external spice model
**********************************************************************
bspice in_b2 out_b2_o pu_bus2_o pd_bus2_o en_b2
+ file=  'test_new.ibs'
+ model= 'spitest'
+ nd_in=in_b1
+ nd_en=en_b1
+ nd_outofin=oti_b2
+ power=off
*--------------------------------------------------------------------*
* bspice Package, Load and power bus 
*--------------------------------------------------------------------*
*------------ Package------------------------------------------------*
rpkg2  out_b2_o pkg2 50.000mOhm
lpkg2  pkg2 out_b2 5.00nH
cpkg2  out_b2 0 2.00pF
*-------------Load---------------------------------------------------*
rload2      out_b2    out_b2_v  50
vload2      out_b2_v  0         5.0
*
*--------------------------------------------------------------------*
* POWER && GND Power Supply Bus
*--------------------------------------------------------------------**
*------------ Package for Power Bus----------------------------------*
rpkgp2  pu_bus2_o pkgp2 50.000mOhm
lpkgp2  pkgp2 pu_bus2 5.00nH
cpkgp2  pu_bus2 0 2.00pF
vppsb2 pu_bus2 0  5.0v
*
rpkgg2  pd_bus2_o pkgg2 50.000mOhm
lpkgg2  pkgg2 pd_bus2 5.00nH
cpkgg2  pd_bus2 0 2.00pF
vgpsb2  pd_bus2 0 0.0v
*
*------------No Package for Power Bus-----------------------------*
*vppsb2 pu_bus2_o 0 5.0
*vgpsb2 pd_bus2_o 0 0.0

*--------------------------------------------------------------------*
* Series Pin Mapping
*--------------------------------------------------------------------*
*
bseries1 out_b1_o out_b2_o
+ file ='test_new.ibs'
+ model='Ext_SPICE_SS_Buff'
+ nd_in=en_b1 $use v(en_b1) as digital signal for nd_in
*
**********************************************************************
* TEST Componet, no [Pin Mapping] ==> Power=on; But [External Circuit]=[Power off]
*  Correlation:
*  pcomp  =   bspice + bibis + bseries1
*  So V(out_b1)=V(pcomp_3) V(out_b2)=V(pcomp_4)
*     V(out_b1_o)=V(pcomp_3_o) V(out_b2_o)=V(pcomp_4_o)
**********************************************************************
.ibis pcomp
+ file = 'test_new.ibs'
+ mod_sel = 'Ext_SPICE_SS=Ext_SPICE_SS_Buff'
+ component = 'TEST'
+ package=1
*
*--------------------------------------------------------------------*
* power supply bus
*--------------------------------------------------------------------*
*
vpower pcomp_1 0 5.0
vgnd   pcomp_2 0 0.0
*
*--------------------------------------------------------------------*
* stimulus of series switch
*--------------------------------------------------------------------*
vbb pcomp_3_4_i en_b1 0 $same with bseries1's voltage between out_b1 out_b2

*--------------------------------------------------------------------*
* component input and load
*--------------------------------------------------------------------*
*
vpls_comp3   pcomp_3_i    0   PWL (
+ 0n 5.0
+ 0.25n 5.0
+ 0.35n 0.0
+ 25.35n 0.0
+ 25.45n 5.0
+ 55n 5.0)

ven_comp3    pcomp_3_en   0  1.0 
*
rload_comp3  pcomp_3    pcomp_3_v 50
vload_comp3  pcomp_3_v  0         5.0
*
vpls_comp4   pcomp_4_i  in_b1 0 
ven_comp4    pcomp_4_en en_b1 0 
*
rload_comp4  pcomp_4   pcomp_4_v 50
vload_comp4  pcomp_4_v 0         5.0
*
**********************************************************************
.END
**********************************************************************
