*
**************** test for ICM [Nodal Path Description] ************
******************************************************************
* 
* NodeMap1
*      --------- --------+ --------- --------- --------+ --------- ---------    NodeMap5
*  --------- --------+ --------- ---------- --------+ ---------- ---------
*                    |   |                          |  |      
*                    |   |                          |  |    
*                    |   |                          |  |      
*                    |   |                          |  |      
*                    |   |                          |  |      
*                    |   |                          |         
*                    |                                 |      
*                    |   |                          |  |      
*                        |                          |  |     
*                    |   |                          |  |      
*                    |   |                          |  |   
*                    |   |                          |  |
*                    |   |                          |
*                    |                               NodeMap3      
*                    |   |       
*  NodeMap4              |     
*                    |   |          
*     --------- -----|--+       
* --------- ---------+          
*                        |      
*                    |   |      
*                    |   |   
*                    |   |      
*                    |   |      
*                    |   |       
*                    |          
*                        |      
*                    |   |      
*                    |   |   
*                    |   |      
*                    |   |   
*                    |   |
*                    |   
*                      NodeMap2  
*
***************** ICM model **************************************
.icm icm1
+ file='test1.icm"
+ model='TwoLineModel1'
+ swath = 1
.opt s_rational_func=0

Vicm1_in1 icm1_NodeMap1_1 0 ac=1.5 pwl(0ns 5v 2ns 0v 5ns 3v 7ns 4v 10ns 0v)
Ricm1_2_1 icm1_NodeMap2_1 0 50
Ricm1_2_2 icm1_NodeMap2_2 0 50
Ricm1_3_1 icm1_NodeMap3_1 0 50
Ricm1_3_2 icm1_NodeMap3_2 0 50
Ricm1_4_1 icm1_NodeMap4_1 0 50
Ricm1_4_2 icm1_NodeMap4_2 0 50
Ricm1_5_1 icm1_NodeMap5_1 0 50
Ricm1_5_2 icm1_NodeMap5_2 0 50

***************** w element *************************************
.include 'sect2_s_2.inc'

s1_1 node1_1    node1_2    inter1_1_1 inter1_1_2  0 mname=sect2_s_2 
s1_2 inter1_1_1 inter1_1_2 inter1_1   inter1_2    0 mname=sect2_s_2

s2_1 inter1_1   inter1_2   inter2_1_1_1 inter2_1_2_1  0 mname=sect2_s_2
s2_1_1 inter2_1_1_1 inter2_1_2_1 inter2_1_1_2 inter2_1_2_2 0 mname=sect2_s_2
s2_1_2 inter2_1_1_2 inter2_1_2_2 inter2_1_1 inter2_1_2 0 mname=sect2_s_2
s2_2 inter2_1_1 inter2_1_2 inter2_2_1 inter2_2_2  0 mname=sect2_s_2
s2_3 inter2_2_1 inter2_2_2 inter2_1   inter2_2    0 mname=sect2_s_2

s3_1 inter2_1   inter2_2   node5_1_1  node5_1_2   0 mname=sect2_s_2
s3_2 node5_1_1  node5_1_2  node5_1    node5_2     0 mname=sect2_s_2

s4_1 inter2_1   inter2_2   node3_1_1  node3_1_2   0 mname=sect2_s_2
s4_2 node3_1_1  node3_1_2  node3_1    node3_2     0 mname=sect2_s_2

s5_1 inter1_1   inter1_2   inter3_1_1 inter3_1_2  0 mname=sect2_s_2
s5_2 inter3_1_1 inter3_1_2 inter3_2_1 inter3_2_2  0 mname=sect2_s_2
s5_3 inter3_2_1 inter3_2_2 inter3_1   inter3_2    0 mname=sect2_s_2

s6_1 inter3_1   inter3_2   node2_2_1  node2_2_2   0 mname=sect2_s_2
s6_2 node2_2_1  node2_2_2  node2_1    node2_2     0 mname=sect2_s_2

s7_1 inter3_1   inter3_2   node4_1_1  node4_1_2   0 mname=sect2_s_2
s7_2 node4_1_1  node4_1_2  node4_1    node4_2     0 mname=sect2_s_2

Vrlgc_in1 node1_1 0 ac=1.5 pwl(0ns 5v 2ns 0v 5ns 3v 7ns 4v 10ns 0v)
Rrlgc_2_1 node2_1 0 50
Rrlgc_2_2 node2_2 0 50
Rrlgc_3_1 node3_1 0 50
Rrlgc_3_2 node3_2 0 50
Rrlgc_4_1 node4_1 0 50
Rrlgc_4_2 node4_2 0 50
Rrlgc_5_1 node5_1 0 50
Rrlgc_5_2 node5_2 0 50

.option post
.tran 0.1ns 10ns
.ac lin 1k 10k 100k 
.end
