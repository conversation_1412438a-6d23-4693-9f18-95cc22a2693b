**********************************************************
*    Analysis And Options
**********************************************************

.op
.tran 10p 30n

.option post=2 probe 
.param vhi = 2.5

**********************************************************
*    Stimulus
**********************************************************

V1 cmp1_3_i 0 0V pulse ( 0V 3V 2n 0.1n 0.1n 7.5n 15n )

**********************************************************
*    Load
**********************************************************
*
Rd1 ebd1_a3 0 50
Rd2 ebd1_a4 0 50

**********************************************************
*    Define Component
**********************************************************
.ibis cmp1
+ file = 'cmpt1.ibs'
+ component = 'Component1'
+ hsp_ver = 2003.3
+ package = 3
+ nowarn

*********************************************************
*   Define EBD
*********************************************************

.ebd ebd1
+ file = 'ebd.ebd'
+ model = 'Board1'
+ component = 'cmp1:u21'

********************************************************
*  Output
********************************************************

.probe tran
+ cmp1_1_out = v(cmp1_1_o)    $ buf_1(Terminator): cmp1_1 
+ cmp1_1_pc  = v(cmp1_1_pc)
+ cmp1_1_gc  = v(cmp1_1_gc)

+ cmp1_2_in  = v(cmp1_2_o)    $ buf_2(Input): cmp1_2
+ cmp1_2_pc  = v(cmp1_2_pc)
+ cmp1_2_gc  = v(cmp1_2_gc)
+ cmp1_2_out_of_in = v(cmp1_2_i)

+ cmp1_3_out = v(cmp1_3_o)    $ buf_3(Output): cmp1_3
+ cmp1_3_pu  = v(cmp1_3_pu)
+ cmp1_3_pd  = v(cmp1_3_pd)
+ cmp1_3_in  = v(cmp1_3_i)

+ ebd1_a3    = v(ebd1_a3)
+ ebd1_a4    = v(ebd1_a4)

.end
