#!/usr/bin/env python3
"""
IBIS to SPICE Converter
将IBIS模型转换为SPICE子电路

使用pybis.py解析.ibs文件，提取模型数据，
并将其转换为SPICE子电路格式，输出到.sp文件中。
"""

import sys
import os
import math
from pybis import IBSParser

def format_spice_value(value, unit=''):
    """
    将数值格式化为SPICE格式
    """
    if value is None or value == 'None':
        return '0'
    
    try:
        val = float(value)
        if val == 0:
            return '0'
        
        # 使用科学记数法或工程记数法
        if abs(val) >= 1e-3 and abs(val) < 1e3:
            return f"{val:.6g}{unit}"
        else:
            return f"{val:.6e}{unit}"
    except:
        return str(value)

def extract_iv_data(iv_range, type_num):
    """
    从IBIS Range对象中提取I-V数据
    返回(voltage_list, current_list)
    """
    if iv_range is None or iv_range[type_num] is None or len(iv_range) == 0:
        return [], []

    # 获取典型值数据 (index 0)
    typ_data = iv_range[type_num]
    if typ_data is None:
        return [], []

    # 解析字符串格式的数据
    data_str = str(typ_data)

    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            voltage_str = data_str[start1:end1]

            # 找到第二个列表的开始和结束
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            current_str = data_str[start2:end2]

            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass

            # 解析电流列表
            currents = []
            for i in current_str.split(', '):
                try:
                    currents.append(float(i.strip()))
                except:
                    pass

            return voltages, currents
        except Exception as e:
            print(f"Error parsing I-V data: {e}")
            print(f"Data string: {data_str}")

    return [], []

def extract_waveform_data(waveform_list, type_num):
    """
    从IBIS波形数据中提取时间-电压数据
    返回(time_list, voltage_list)
    """
    if not waveform_list or len(waveform_list) == 0:
        return [], []

    # 获取第一个波形数据（典型值）
    waveform = waveform_list[type_num]
    if 'waveform' not in waveform:
        return [], []

    waveform_data = waveform['waveform'][type_num]  # 获取典型值
    if waveform_data is None:
        return [], []

    # 解析字符串格式的数据
    data_str = str(waveform_data)

    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束（时间）
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            time_str = data_str[start1:end1]

            # 找到第二个列表的开始和结束（电压）
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            voltage_str = data_str[start2:end2]

            # 解析时间列表
            times = []
            for t in time_str.split(', '):
                try:
                    times.append(float(t.strip()))
                except:
                    pass

            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass

            return times, voltages
        except Exception as e:
            print(f"Error parsing waveform data: {e}")
            print(f"Data string: {data_str}")

    return [], []

def create_pwl_source(name, voltages, currents, node1, node2):
    """
    创建分段线性电流源
    """
    if not voltages or not currents or len(voltages) != len(currents):
        return f"* Warning: Invalid I-V data for {name}\n"
    
    spice_lines = []
    spice_lines.append(f"* {name} I-V characteristic")
    
    # 创建电压控制电流源
    spice_lines.append(f"G{name} {node1} {node2} VCCS PWL(")
    
    # 添加PWL数据点
    for i, (v, i_val) in enumerate(zip(voltages, currents)):
        if i % 4 == 0 and i > 0:  # 每行4个数据点
            spice_lines.append("")
            spice_lines.append("+")
        spice_lines.append(f" {format_spice_value(v)}V {format_spice_value(i_val)}A")
    
    spice_lines.append(" )")
    spice_lines.append("")
    
    return "\n".join(spice_lines)

def determine_model_features(model_type):
    """
    根据IBIS模型类型确定需要的功能特性
    返回: (needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential)
    """
    # 标准化模型类型名称（去除大小写差异和特殊字符）
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')

    # 需要ENABLE信号的模型类型
    enable_types = {
        '3state', 'io', 'ioopendrain', 'ioopensink', 'ioopensource',
        '3stateecl', 'ioecl', 'iodiff', '3statediff', 'seriesswitch'
    }

    # 需要波形数据的模型类型（有输出驱动能力的）
    waveform_types = {
        'output', '3state', 'io', 'opendrain', 'ioopendrain',
        'opensink', 'ioopensink', 'opensource', 'ioopensource',
        'outputecl', '3stateecl', 'ioecl', 'outputdiff',
        '3statediff', 'iodiff', 'series', 'seriesswitch'
    }

    # 需要Pullup/Pulldown的模型类型（推挽输出）
    pullup_pulldown_types = {
        'output', '3state', 'io', 'outputecl', '3stateecl',
        'ioecl', 'outputdiff', '3statediff', 'iodiff', 'series'
    }

    # 差分信号类型
    differential_types = {
        'inputdiff', 'outputdiff', 'iodiff', '3statediff'
    }

    # 开漏/开源类型（只有单向驱动）
    open_drain_types = {
        'opendrain', 'ioopendrain', 'opensource', 'ioopensource', 'opensink', 'ioopensink'
    }

    needs_enable = model_type_norm in enable_types
    needs_waveforms = model_type_norm in waveform_types
    needs_pullup_pulldown = model_type_norm in pullup_pulldown_types
    needs_differential = model_type_norm in differential_types

    return needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential

def get_model_type_description(model_type):
    """
    获取IBIS模型类型的详细描述
    """
    descriptions = {
        'Input': 'Input buffer - receives signals only',
        'Output': 'Output buffer - drives signals with push-pull output',
        'I/O': 'Bidirectional I/O buffer - can both receive and drive',
        '3-state': 'Three-state buffer - output can be disabled (Hi-Z)',
        'Open_drain': 'Open drain output - can only pull low',
        'I/O_open_drain': 'Bidirectional open drain I/O',
        'Open_sink': 'Open sink output - current sink only',
        'I/O_open_sink': 'Bidirectional open sink I/O',
        'Open_source': 'Open source output - current source only',
        'I/O_open_source': 'Bidirectional open source I/O',
        'Input_ECL': 'ECL input buffer - differential ECL logic',
        'Output_ECL': 'ECL output buffer - differential ECL driver',
        'I/O_ECL': 'Bidirectional ECL I/O buffer',
        '3-state_ECL': 'Three-state ECL buffer with enable control',
        'Input_diff': 'Differential input buffer - LVDS/CML receiver',
        'Output_diff': 'Differential output buffer - LVDS/CML driver',
        'I/O_diff': 'Bidirectional differential I/O buffer',
        '3-state_diff': 'Three-state differential buffer',
        'Series': 'Series termination buffer - source termination',
        'Series_switch': 'Switchable series termination buffer',
        'Terminator': 'Passive termination element'
    }

    return descriptions.get(model_type, f'Unknown model type: {model_type}')

def convert_ibis_to_spice(root, model_name, output_file, type_num):
    """
    将IBIS模型转换为SPICE子电路
    """
    if 'Model' not in root or model_name not in root['Model']:
        print(f"Error: Model '{model_name}' not found in IBIS file")
        return False

    model = root['Model'][model_name]

    # 检查模型类型
    model_type = model.get('Model_type', '3-state')  # 默认为3-state
    print(f"Model type: {model_type}")

    # 根据模型类型确定端口和功能
    needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential = determine_model_features(model_type)

    print(f"Features: Enable={needs_enable}, Waveforms={needs_waveforms}, Pullup/Pulldown={needs_pullup_pulldown}, Differential={needs_differential}")
    
    # 开始生成SPICE文件
    spice_content = []
    spice_content.append("* SPICE Subcircuit generated from IBIS model")
    spice_content.append(f"* Model: {model_name}")
    spice_content.append(f"* Model Type: {model_type}")
    spice_content.append("* Generated by ibis_to_spice_converter.py")
    spice_content.append("")

    # 添加模型类型说明
    model_description = get_model_type_description(model_type)
    if model_description:
        spice_content.append(f"* {model_description}")
        spice_content.append("")
    
    # 根据模型类型生成子电路定义
    if needs_differential:
        # 差分信号需要正负两个端口
        ports = ["PAD_P", "PAD_N", "VCC", "VSS"]
        port_comments = [
            "* PAD_P    - Positive differential pad",
            "* PAD_N    - Negative differential pad",
            "* VCC      - Power supply",
            "* VSS      - Ground"
        ]
    else:
        # 单端信号
        ports = ["PAD", "VCC", "VSS"]
        port_comments = [
            "* PAD      - I/O pad connection",
            "* VCC      - Power supply",
            "* VSS      - Ground"
        ]

    if needs_enable:
        ports.append("ENABLE")
        port_comments.append("* ENABLE   - Enable signal (for 3-state/I/O models)")

    if needs_waveforms:
        if needs_differential:
            ports.extend(["PAD_P_RISE", "PAD_P_FALL", "PAD_N_RISE", "PAD_N_FALL"])
            port_comments.extend([
                "* PAD_P_RISE - Positive rising waveform reference",
                "* PAD_P_FALL - Positive falling waveform reference",
                "* PAD_N_RISE - Negative rising waveform reference",
                "* PAD_N_FALL - Negative falling waveform reference"
            ])
        else:
            ports.extend(["PAD_RISE", "PAD_FALL"])
            port_comments.extend([
                "* PAD_RISE - Rising waveform reference output",
                "* PAD_FALL - Falling waveform reference output"
            ])

    # 子电路定义
    spice_content.append(f".SUBCKT {model_name} {' '.join(ports)}")
    spice_content.extend(port_comments)
    spice_content.append("")
    
    # 添加模型参数
    if 'C_comp' in model:
        c_comp = model['C_comp'][type_num] if isinstance(model['C_comp'], list) else model['C_comp']
        spice_content.append(f"* Input/Output Capacitance: {format_spice_value(c_comp, 'F')}")
        spice_content.append(f"C_comp PAD VSS {format_spice_value(c_comp, 'F')}")
        spice_content.append("")
    
    # 电压范围信息
    if 'Voltage Range' in model:
        vrange = model['Voltage Range']
        spice_content.append(f"* Voltage Range: {vrange}")
        spice_content.append("")
    
    # 温度范围信息
    if 'Temperature Range' in model:
        trange = model['Temperature Range']
        spice_content.append(f"* Temperature Range: {trange}")
        spice_content.append("")
    
    # 检查是否为开漏/开源类型
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')
    is_open_drain = 'opendrain' in model_type_norm or 'opensink' in model_type_norm
    is_open_source = 'opensource' in model_type_norm

    # 处理Pulldown特性
    if needs_pullup_pulldown and 'Pulldown' in model and not is_open_source:
        voltages, currents = extract_iv_data(model['Pulldown'], type_num)
        if voltages and currents:
            spice_content.append("* Pulldown I-V Characteristic")
            table_line = "Gpd PAD VSS TABLE {V(PAD,VSS)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                table_pairs.append(f"({format_spice_value(v)},{format_spice_value(i_val)})")

            # 每行最多5个数据对，避免行过长
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")

    # 处理Pullup特性（排除开漏类型）
    if needs_pullup_pulldown and 'Pullup' in model and not is_open_drain:
        voltages, currents = extract_iv_data(model['Pullup'], type_num)
        if voltages and currents:
            spice_content.append("* Pullup I-V Characteristic")
            table_line = "Gpu VCC PAD TABLE {V(VCC,PAD)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                # 对于pullup，电压相对于VCC，电流取绝对值
                v_rel = 5.0 - v  # 假设VCC=5V
                table_pairs.append(f"({format_spice_value(v_rel)},{format_spice_value(abs(i_val))})")

            # 每行最多5个数据对
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")
    
    # 处理GND Clamp
    if 'GND Clamp' in model:
        voltages, currents = extract_iv_data(model['GND Clamp'], type_num)
        if voltages and currents:
            spice_content.append("* GND Clamp I-V Characteristic")
            table_line = "Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                # 对于GND clamp，使用负电压的绝对值
                table_pairs.append(f"({format_spice_value(abs(v))},{format_spice_value(abs(i_val))})")

            # 每行最多5个数据对
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")

    # 处理POWER Clamp
    if 'POWER Clamp' in model:
        voltages, currents = extract_iv_data(model['POWER Clamp'], type_num)
        if voltages and currents:
            spice_content.append("* POWER Clamp I-V Characteristic")
            table_line = "Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = "

            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                # 对于POWER clamp，电压相对于VCC
                v_rel = 5.0 + v  # VCC + 负电压
                table_pairs.append(f"({format_spice_value(v_rel)},{format_spice_value(i_val)})")

            # 每行最多5个数据对
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")
    
    # 处理Ramp信息
    if 'Ramp' in model:
        ramp = model['Ramp']
        spice_content.append("* Ramp Information")
        if 'dV/dt_r' in ramp:
            dvdt_r = ramp['dV/dt_r']
            spice_content.append(f"* Rising edge dV/dt: {dvdt_r}")
        if 'dV/dt_f' in ramp:
            dvdt_f = ramp['dV/dt_f']
            spice_content.append(f"* Falling edge dV/dt: {dvdt_f}")
        if 'R_load' in ramp:
            r_load = ramp['R_load']
            spice_content.append(f"* Load resistance: {format_spice_value(r_load, 'Ohm')}")
        spice_content.append("")

    # 处理Rising Waveform波形数据（仅对有波形输出的模型）
    if needs_waveforms and 'Rising Waveform' in model:
        times, voltages = extract_waveform_data(model['Rising Waveform'], type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Rising Waveform V-T Characteristic")
            waveform_info = model['Rising Waveform'][0]

            # 添加波形测试条件信息
            if 'R_fixture' in waveform_info:
                spice_content.append(f"* R_fixture = {format_spice_value(waveform_info['R_fixture'], 'Ohm')}")
            if 'V_fixture' in waveform_info:
                v_fix = waveform_info['V_fixture'][0] if isinstance(waveform_info['V_fixture'], list) else waveform_info['V_fixture']
                spice_content.append(f"* V_fixture = {format_spice_value(v_fix, 'V')}")
            if 'C_fixture' in waveform_info:
                spice_content.append(f"* C_fixture = {format_spice_value(waveform_info['C_fixture'], 'F')}")

            # 创建分段线性电压源用于上升沿
            spice_content.append("* Rising edge voltage source (for timing analysis)")
            pwl_line = "Vrise_ref PAD_RISE 0 PWL("

            # 构建PWL数据点
            pwl_pairs = []
            for t, v in zip(times, voltages):
                pwl_pairs.append(f"{format_spice_value(t, 's')} {format_spice_value(v, 'V')}")

            # 每行最多4个时间-电压对
            pairs_per_line = 4
            for i in range(0, len(pwl_pairs), pairs_per_line):
                line_pairs = pwl_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(pwl_line + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
                else:
                    spice_content.append("+ " + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
            spice_content.append("")
        else:
            spice_content.append("* Rising Waveform data available but could not be parsed")
            spice_content.append("")

    # 处理Falling Waveform波形数据（仅对有波形输出的模型）
    if needs_waveforms and 'Falling Waveform' in model:
        times, voltages = extract_waveform_data(model['Falling Waveform'], type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Falling Waveform V-T Characteristic")
            waveform_info = model['Falling Waveform'][0]

            # 添加波形测试条件信息
            if 'R_fixture' in waveform_info:
                spice_content.append(f"* R_fixture = {format_spice_value(waveform_info['R_fixture'], 'Ohm')}")
            if 'V_fixture' in waveform_info:
                v_fix = waveform_info['V_fixture'][0] if isinstance(waveform_info['V_fixture'], list) else waveform_info['V_fixture']
                spice_content.append(f"* V_fixture = {format_spice_value(v_fix, 'V')}")
            if 'C_fixture' in waveform_info:
                spice_content.append(f"* C_fixture = {format_spice_value(waveform_info['C_fixture'], 'F')}")

            # 创建分段线性电压源用于下降沿
            spice_content.append("* Falling edge voltage source (for timing analysis)")
            pwl_line = "Vfall_ref PAD_FALL 0 PWL("

            # 构建PWL数据点
            pwl_pairs = []
            for t, v in zip(times, voltages):
                pwl_pairs.append(f"{format_spice_value(t, 's')} {format_spice_value(v, 'V')}")

            # 每行最多4个时间-电压对
            pairs_per_line = 4
            for i in range(0, len(pwl_pairs), pairs_per_line):
                line_pairs = pwl_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(pwl_line + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
                else:
                    spice_content.append("+ " + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
            spice_content.append("")
        else:
            spice_content.append("* Falling Waveform data available but could not be parsed")
            spice_content.append("")
    
    # 结束子电路
    spice_content.append(".ENDS")
    spice_content.append("")
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(spice_content))
        print(f"SPICE subcircuit successfully written to {output_file}")
        return True
    except Exception as e:
        print(f"Error writing to file {output_file}: {e}")
        return False

def main_process(ibis_file, model_name, output_file, type_num):
    """
    主函数
    """
    
    print(f"Parsing IBIS file: {ibis_file}")
    try:
        parser = IBSParser()
        with open(ibis_file, 'r', encoding='utf-8') as f:
            root = parser.parse(f)
        
        print(f"Successfully parsed IBIS file")
        print(f"Converting model '{model_name}' to SPICE subcircuit...")
        
        # 转换为SPICE
        success = convert_ibis_to_spice(root, model_name, output_file, type_num)
        
        if success:
            print(f"Conversion completed successfully!")
            print(f"Output file: {output_file}")
        else:
            print("Conversion failed!")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    ibis_file = "ibis/at16245.ibs"
    model_name = "AT16245_OUT"
    output_file = "AT16245_OUT.sp"
    type_num = 0
    main_process(ibis_file, model_name, output_file, type_num)
