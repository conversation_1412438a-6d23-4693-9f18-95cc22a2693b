#!/usr/bin/env python3

import sys
import os
import math
from pybis import IBSParser

def format_spice_value(value, unit=''):
    if value is None or value == 'None':
        return '0'
    
    try:
        val = float(value)
        if val == 0:
            return '0'
        
        if abs(val) >= 1e-3 and abs(val) < 1e3:
            return f"{val:.6g}{unit}"
        else:
            return f"{val:.6e}{unit}"
    except:
        return str(value)

def extract_iv_data(iv_range, type_num):
    if iv_range is None or len(iv_range) == 0:
        return [], []
    
    typ_data = iv_range[type_num]
    if typ_data is None:
        return [], []
    
    data_str = str(typ_data)
    
    if '([' in data_str and '], [' in data_str:
        try:
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            voltage_str = data_str[start1:end1]
            
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            current_str = data_str[start2:end2]
            
            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass
            
            # 解析电流列表
            currents = []
            for i in current_str.split(', '):
                try:
                    currents.append(float(i.strip()))
                except:
                    pass
            
            return voltages, currents
        except Exception as e:
            print(f"Error parsing I-V data: {e}")
            print(f"Data string: {data_str}")
    
    return [], []

def extract_waveform_data(waveform_list, type_num):
    if not waveform_list or len(waveform_list) == 0:
        return [], []
    
    waveform = waveform_list[type_num]
    if 'waveform' not in waveform:
        return [], []
    
    waveform_data = waveform['waveform'][type_num]
    if waveform_data is None:
        return [], []
    
    data_str = str(waveform_data)
    
    if '([' in data_str and '], [' in data_str:
        try:
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            time_str = data_str[start1:end1]
            
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            voltage_str = data_str[start2:end2]
            
            # 解析时间列表
            times = []
            for t in time_str.split(', '):
                try:
                    times.append(float(t.strip()))
                except:
                    pass
            
            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass
            
            return times, voltages
        except Exception as e:
            print(f"Error parsing waveform data: {e}")
            print(f"Data string: {data_str}")
    
    return [], []

def convert_ibis_to_spice(root, model_name, output_file, type_num):
    if 'Model' not in root or model_name not in root['Model']:
        print(f"Error: Model '{model_name}' not found in IBIS file")
        return False
    
    model = root['Model'][model_name]
    
    # 开始生成SPICE文件
    spice_content = []
    spice_content.append("* SPICE Subcircuit generated from IBIS model")
    spice_content.append(f"* Model: {model_name}")
    spice_content.append("* Generated by ibis_to_spice_converter.py - Original Version")
    spice_content.append("")
    
    # 硬编码的子电路定义
    spice_content.append(f".SUBCKT {model_name} PAD VCC VSS ENABLE PAD_RISE PAD_FALL")
    spice_content.append("* PAD      - I/O pad connection")
    spice_content.append("* VCC      - Power supply")
    spice_content.append("* VSS      - Ground")
    spice_content.append("* ENABLE   - Enable signal (for 3-state models)")
    spice_content.append("* PAD_RISE - Rising waveform reference output")
    spice_content.append("* PAD_FALL - Falling waveform reference output")
    spice_content.append("")
    
    # 添加模型参数
    if 'C_comp' in model:
        c_comp = model['C_comp'][type_num] if isinstance(model['C_comp'], list) else model['C_comp']
        spice_content.append(f"* Input/Output Capacitance: {format_spice_value(c_comp, 'F')}")
        spice_content.append(f"C_comp PAD VSS {format_spice_value(c_comp, 'F')}")
        spice_content.append("")

    # 处理Pullup特性
    if 'Pullup' in model:
        voltages, currents = extract_iv_data(model['Pullup'], type_num)
        if voltages and currents:
            spice_content.append("* Pullup I-V Characteristic")
            spice_content.append("Gpu PAD VSS TABLE {V(VCC,PAD)} =")
            for i, (v, i_val) in enumerate(zip(voltages, currents)):
                v = 5.0 - v
                spice_content.append(f" +({format_spice_value(v)} {format_spice_value(abs(i_val))})")
            spice_content.append("")

    # 处理Pulldown特性
    if 'Pulldown' in model:
        voltages, currents = extract_iv_data(model['Pulldown'], type_num)
        if voltages and currents:
            spice_content.append("* Pulldown I-V Characteristic")
            spice_content.append("Gpd PAD VSS TABLE {V(PAD,VSS)} =")
            for i, (v, i_val) in enumerate(zip(voltages, currents)):
                spice_content.append(f" +({format_spice_value(v)} {format_spice_value(i_val)})")
            spice_content.append("")

    # 处理POWER Clamp保护钳位
    if 'POWER Clamp' in model:
        voltages, currents = extract_iv_data(model['POWER Clamp'], type_num)
        if voltages and currents:
            spice_content.append("* POWER Clamp I-V Characteristic")
            spice_content.append("Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} =")
            for i, (v, i_val) in enumerate(zip(voltages, currents)):
                v = 5.0 + v
                spice_content.append(f" +({format_spice_value(v)} {format_spice_value(i_val)})")
            spice_content.append("")

    # 处理GND Clamp保护钳位
    if 'GND Clamp' in model:
        voltages, currents = extract_iv_data(model['GND Clamp'], type_num)
        if voltages and currents:
            spice_content.append("* GND Clamp I-V Characteristic")
            spice_content.append("Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} =")
            for i, (v, i_val) in enumerate(zip(voltages, currents)):
                spice_content.append(f" +({format_spice_value(abs(v))} {format_spice_value(abs(i_val))})")
            spice_content.append("")
    
    # 处理Rising Waveform波形数据
    if 'Rising Waveform' in model:
        times, voltages = extract_waveform_data(model['Rising Waveform'], type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Rising Waveform V-T Characteristic")
            spice_content.append("Vrise_ref PAD_RISE 0 PWL(")
            for i, (t, v) in enumerate(zip(times, voltages)):
                line = f" +{format_spice_value(t, 's')} {format_spice_value(v, 'V')}"
                if i == len(times)-1:
                    line += ")"
                spice_content.append(line)
            spice_content.append("")

    # 处理falling Waveform波形数据
    if 'falling Waveform' in model:
        times, voltages = extract_waveform_data(model['Falling Waveform'], type_num)
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Falling Waveform V-T Characteristic")
            spice_content.append("Vfall_ref PAD_FALL 0 PWL(")
            for i, (t, v) in enumerate(zip(times, voltages)):
                line = f" +{format_spice_value(t, 's')} {format_spice_value(v, 'V')}"
                if i == len(times) - 1:
                    line += ")"
                spice_content.append(line)
            spice_content.append("")

    # 结束子电路
    spice_content.append(".ENDS")
    spice_content.append("")
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(spice_content))
        print(f"SPICE subcircuit successfully written to {output_file}")
        return True
    except Exception as e:
        print(f"Error writing to file {output_file}: {e}")
        return False

def convert_main(ibis_file, model_name, output_file, type_num=0):
    
    print(f"Parsing IBIS file: {ibis_file}")
    
    try:
        parser = IBSParser()
        with open(ibis_file, 'r', encoding='utf-8') as f:
            root = parser.parse(f)
        
        print(f"Successfully parsed IBIS file")
        print(f"Converting model '{model_name}' to SPICE subcircuit...")
        
        # 转换为SPICE
        success = convert_ibis_to_spice(root, model_name, output_file, type_num)
        
        if success:
            print(f"Conversion completed successfully!")
            print(f"Output file: {output_file}")
        else:
            print("Conversion failed!")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    ibis_file = "ibis/at16245.ibs"
    # ibis_file = "samples/bushold.ibs"
    model_name = "AT16245_OUT"
    # model_name = "TOP_MODEL_BUS_HOLD"
    output_file = "AT16245_OUTPUT_v1.sp"
    # output_file = "TOP_MODEL_BUS_HOLD.sp"
    convert_main(ibis_file, model_name, output_file)
