[IBIS Ver]       3.2
[File Name]      dclamptr.ibs
[File Rev]       0.2
[Date]           June 24, 1998, updated Sept. 26, 1998
[Source]         Artifical Data
[Notes]          This data is a sample, only.
|                This file demonstrates Dynamic Clamp - Triggered Mode.
[Disclaimer]     This information is for modeling purposes and is not
|                guaranteed.  
[Copyright]      None - public sample
|
|**************************************************************************
|                            Component TRIGGERED-DYNAMIC-CLAMP-SAMPLE
|**************************************************************************
|
[Component]      TRIGGERED-DYNAMIC-CLAMP-SAMPLE
[Manufacturer]   None 
[Package]
| variable       typ                 min                 max
R_pkg            200m                100m                300m
L_pkg            4.32nH              3.34nH              5.30nH
C_pkg            0.38pF              0.33pF              0.43pF
|
|**************************************************************************
|
[Pin]  signal_name          model_name           R_pin     L_pin     C_pin
|
  1    Sample1              TOP_MODEL_D_CLMP
 12    GND                  GND
 24    VCC                  POWER
|
|**************************************************************************
|                             Model TOP_MODEL_D_CLMP
|**************************************************************************
|
[Model]             TOP_MODEL_D_CLMP
Model_type          Input
Vinh = 2.0
Vinl = 0.8
|                   
C_comp              4pF        3pF       5pF | Includes Submodel contribution
|
[Add Submodel]
| Submodel          Submodel_mode
TRIGGERED_DCLMP     Non-Driving    | Could also use All          
|
|
|                   typ        min       max
[Voltage Range]     5.0        4.5       5.5
|
[GND Clamp]
|
-2.0000e+00        -6.158e+17  NA        NA
-1.9000e+00        -1.697e+16  NA        NA
-1.8000e+00        -4.679e+14  NA        NA
-1.7000e+00        -1.290e+13  NA        NA
-1.6000e+00        -3.556e+11  NA        NA
-1.5000e+00        -9.802e+09  NA        NA
-1.4000e+00        -2.702e+08  NA        NA
-1.3000e+00        -7.449e+06  NA        NA
-1.2000e+00        -2.053e+05  NA        NA
-1.1000e+00        -5.660e+03  NA        NA
-1.0000e+00        -1.560e+02  NA        NA
-9.0000e-01        -4.308e+00  NA        NA
-8.0000e-01        -1.221e-01  NA        NA
-7.0000e-01        -4.315e-03  NA        NA
-6.0000e-01        -1.715e-04  NA        NA
-5.0000e-01        -4.959e-06  NA        NA
-4.0000e-01        -1.373e-07  NA        NA
-3.0000e-01        -4.075e-09  NA        NA
-2.0000e-01        -3.044e-10  NA        NA
-1.0000e-01        -1.030e-10  NA        NA
   0.               0          NA        NA
   5                0          NA        NA
|
[POWER Clamp]
|
-2.0000e+00         6.158e+17  NA        NA
-1.9000e+00         1.697e+16  NA        NA
-1.8000e+00         4.679e+14  NA        NA
-1.7000e+00         1.290e+13  NA        NA
-1.6000e+00         3.556e+11  NA        NA
-1.5000e+00         9.802e+09  NA        NA
-1.4000e+00         2.702e+08  NA        NA
-1.3000e+00         7.449e+06  NA        NA
-1.2000e+00         2.053e+05  NA        NA
-1.1000e+00         5.660e+03  NA        NA
-1.0000e+00         1.560e+02  NA        NA
-9.0000e-01         4.308e+00  NA        NA
-8.0000e-01         1.221e-01  NA        NA
-7.0000e-01         4.315e-03  NA        NA
-6.0000e-01         1.715e-04  NA        NA
-5.0000e-01         4.959e-06  NA        NA
-4.0000e-01         1.373e-07  NA        NA
-3.0000e-01         4.075e-09  NA        NA
-2.0000e-01         3.044e-10  NA        NA
-1.0000e-01         1.030e-10  NA        NA
   0.               0          NA        NA
   5                0          NA        NA
|
|
|**************************************************************************
|                             Submodel TRIGGERED_DCLMP
|**************************************************************************
|
| Example of Dynamic clamp Model with both dynamic GND and POWER clamps:
|
[Submodel]       TRIGGERED_DCLMP
Submodel_type    Dynamic_clamp
|
| Note, Submodels do not have C_comp
|
[Submodel Spec]
|   Subparameter          typ        min        max
|
V_trigger_f               1.4        1.2        1.6  | Falling edge trigger
V_trigger_r               3.6        2.9        4.3  | Rising edge trigger
|
|                         typ        min        max
| [Voltage Range]           5.0        4.5        5.5 
| Note, the actual voltage range and reference voltages are inherited from
| the top-level model.
|
[GND Pulse Table]                                    | GND Clamp offset table
|    Time          V(typ)       V(min)        V(max)
|
       0             0            0             0
    1e-9             0            0             0
    2e-9           0.9          0.8           1.0
   10e-9           0.9          0.8           1.0
   11e-9             0            0             0 
|
[GND Clamp]                                          | Table to be offset
|
|  Voltage        I(typ)       I(min)        I(max)
|
    -5.000     -3.300e+01    -3.000e+01    -3.500e+01
    -4.000     -2.300e+01    -2.200e+01    -2.400e+01
    -3.000     -1.300e+01    -1.200e+01    -1.400e+01
    -2.000     -3.000e+00    -2.300e+00    -3.700e+00
    -1.900     -2.100e+00    -1.500e+00    -2.800e+00
    -1.800     -1.300e+00    -8.600e-01    -1.900e+00
    -1.700     -6.800e-01    -4.000e-01    -1.100e+00
    -1.600     -2.800e-01    -1.800e-01    -5.100e-01
    -1.500     -1.200e-01    -9.800e-02    -1.800e-01
    -1.400     -7.500e-02    -7.100e-02    -8.300e-02
    -1.300     -5.750e-02    -5.700e-02    -5.900e-02
    -1.200     -4.600e-02    -4.650e-02    -4.550e-02
    -1.100     -3.550e-02    -3.700e-02    -3.450e-02
    -1.000     -2.650e-02    -2.850e-02    -2.500e-02
    -0.900     -1.850e-02    -2.100e-02    -1.650e-02
    -0.800     -1.200e-02    -1.400e-02    -9.750e-03
    -0.700     -6.700e-03    -8.800e-03    -4.700e-03
    -0.600     -3.000e-03    -4.650e-03    -1.600e-03
    -0.500     -9.450e-04    -1.950e-03    -3.650e-04
    -0.400     -5.700e-05    -2.700e-04    -5.550e-06
    -0.300     -1.200e-06    -1.200e-05    -5.500e-08
    -0.200     -3.000e-08    -5.000e-07     0.000e+00
    -0.100      0.000e+00     0.000e+00     0.000e+00
     0.000      0.000e+00     0.000e+00     0.000e+00
     5.000      0.000e+00     0.000e+00     0.000e+00
|
[POWER Pulse Table]                                 | POWER Clamp offset table
|
|    Time          V(typ)       V(min)        V(max)
|
       0             0            0             0
    1e-9             0            0             0
    2e-9          -0.9         -1.0          -0.8
   10e-9          -0.9         -1.0          -0.8
   11e-9             0            0             0 
|
[POWER Clamp]                                       | Table to be offset
|
|  Voltage        I(typ)        I(min)        I(max)
|
    -5.000      1.150e+01     1.100e+01     1.150e+01
    -4.000      7.800e+00     7.500e+00     8.150e+00
    -3.000      4.350e+00     4.100e+00     4.700e+00
    -2.000      1.100e+00     8.750e-01     1.300e+00
    -1.900      8.000e-01     6.050e-01     1.000e+00
    -1.800      5.300e-01     3.700e-01     7.250e-01
    -1.700      2.900e-01     1.800e-01     4.500e-01
    -1.600      1.200e-01     6.850e-02     2.200e-01
    -1.500      3.650e-02     2.400e-02     6.900e-02
    -1.400      1.200e-02     1.100e-02     1.600e-02
    -1.300      6.300e-03     6.650e-03     6.100e-03
    -1.200      4.200e-03     4.750e-03     3.650e-03
    -1.100      2.900e-03     3.500e-03     2.350e-03
    -1.000      1.900e-03     2.450e-03     1.400e-03
    -0.900      1.150e-03     1.600e-03     7.100e-04
    -0.800      5.500e-04     9.150e-04     2.600e-04
    -0.700      1.200e-04     4.400e-04     5.600e-05
    -0.600      5.400e-05     1.550e-04     1.200e-05
    -0.500      1.350e-05     5.400e-05     1.300e-06
    -0.400      8.650e-07     7.450e-06     4.950e-08
    -0.300      6.250e-08     7.550e-07     0.000e+00
    -0.200      0.000e+00     8.400e-08     0.000e+00
    -0.100      0.000e+00     0.000e-08     0.000e+00
     0.000      0.000e+00     0.000e+00     0.000e+00
|
[End]
