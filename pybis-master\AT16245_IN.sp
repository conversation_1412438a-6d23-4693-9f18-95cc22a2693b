* SPICE Subcircuit generated from IBIS model
* Model: AT16245_IN
* Generated by ibis_to_spice_converter.py

.SUBCKT AT16245_IN PAD VCC VSS
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground

* Input/Output Capacitance: 3.400000e-12F
C_comp PAD VSS 3.400000e-12F

* Voltage Range: [5.0, 4.75, 5.25]

* Temperature Range: [25.0, 0.0, 85.0]

* GND Clamp I-V Characteristic
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (5,4.269) (4.9,4.17) (4.8,4.071) (4.7,3.972) (4.6,3.874)
+ (4.5,3.775) (4.4,3.676) (4.3,3.577) (4.2,3.478) (4.1,3.38)
+ (4,3.281) (3.9,3.182) (3.8,3.083) (3.7,2.985) (3.6,2.886)
+ (3.5,2.787) (3.4,2.689) (3.3,2.59) (3.2,2.492) (3.1,2.393)
+ (3,2.295) (2.9,2.196) (2.8,2.098) (2.7,2) (2.6,1.902)
+ (2.5,1.804) (2.4,1.705) (2.3,1.607) (2.2,1.51) (2.1,1.412)
+ (2,1.314) (1.9,1.217) (1.8,1.119) (1.7,1.022) (1.6,0.9255)
+ (1.5,0.8288) (1.4,0.7325) (1.3,0.6366) (1.2,0.5412) (1.1,0.4467)
+ (1,0.3532) (0.9,0.2614) (0.8,0.1726) (0.7,0.08983) (0.6,0.02419)
+ (0.5,0.001292) (0.4,5.192000e-05) (0.3,1.779000e-05) (0.2,1.129000e-05) (0.1,5.577000e-06)
+ (0,0) (5,0)

* POWER Clamp I-V Characteristic
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (0,4.217) (0.1,4.119) (0.2,4.02) (0.3,3.922) (0.4,3.824)
+ (0.5,3.726) (0.6,3.628) (0.7,3.53) (0.8,3.432) (0.9,3.333)
+ (1,3.235) (1.1,3.137) (1.2,3.039) (1.3,2.941) (1.4,2.843)
+ (1.5,2.746) (1.6,2.648) (1.7,2.55) (1.8,2.452) (1.9,2.354)
+ (2,2.257) (2.1,2.159) (2.2,2.061) (2.3,1.964) (2.4,1.866)
+ (2.5,1.769) (2.6,1.671) (2.7,1.574) (2.8,1.477) (2.9,1.38)
+ (3,1.283) (3.1,1.186) (3.2,1.089) (3.3,0.9933) (3.4,0.8971)
+ (3.5,0.8012) (3.6,0.7056) (3.7,0.6105) (3.8,0.5159) (3.9,0.4222)
+ (4,0.3297) (4.1,0.2391) (4.2,0.1518) (4.3,0.07194) (4.4,0.01426)
+ (4.5,5.020000e-04) (4.6,1.075000e-05) (4.7,0) (5,0)

.ENDS
