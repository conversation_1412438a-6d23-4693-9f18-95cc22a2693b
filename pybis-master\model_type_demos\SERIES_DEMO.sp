* SPICE Subcircuit Demo
* Model: SERIES_DEMO
* Model Type: Series
* Series termination buffer - source termination

.SUBCKT SERIES_DEMO PAD VCC VSS PAD_RISE PAD_FALL
* PAD      - I/O pad connection
* VCC      - Power supply
* VSS      - Ground
* PAD_RISE - Rising waveform reference output
* PAD_FALL - Falling waveform reference output

* Input/Output capacitance
C_comp PAD VSS 3pF

* Pulldown I-V Characteristic
Gpd PAD VSS TABLE {V(PAD,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)

* Pullup I-V Characteristic
Gpu VCC PAD TABLE {V(VCC,PAD)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)

* Protection clamps
Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (0.7,0) (0.8,0.01) (0.9,0.1)
Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)

* Timing waveforms
Vrise PAD_RISE 0 PWL(0 0V 1ns 2.5V 2ns 5V)
Vfall PAD_FALL 0 PWL(0 5V 1ns 2.5V 2ns 0V)

* Series termination resistance
Rseries PAD PAD_INT 25

.ENDS
