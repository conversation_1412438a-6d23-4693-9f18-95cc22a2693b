#!/usr/bin/env python3
"""
IBIS to SPICE Converter - Format Fixed Version (v2.0)
将IBIS模型转换为SPICE子电路

v2.0版本特点：
- 修正了TABLE格式：TABLE {expression} = (x0,y0) (x1,y1) (x2,y2)
- 修正了PWL格式：PWL(t0 v0 t1 v1 t2 v2 ...)
- 续行符+与数据在同一行
- 移除了单独占行的括号和符号
- 仍然硬编码ENABLE端口（未解决模型类型问题）
"""

import sys
import os
import math
from pybis import IBSParser

def format_spice_value(value, unit=''):
    """
    将数值格式化为SPICE格式
    """
    if value is None or value == 'None':
        return '0'
    
    try:
        val = float(value)
        if val == 0:
            return '0'
        
        # 使用科学记数法或工程记数法
        if abs(val) >= 1e-3 and abs(val) < 1e3:
            return f"{val:.6g}{unit}"
        else:
            return f"{val:.6e}{unit}"
    except:
        return str(value)

def extract_iv_data(iv_range):
    """
    从IBIS Range对象中提取I-V数据
    返回(voltage_list, current_list)
    """
    if iv_range is None or len(iv_range) == 0:
        return [], []
    
    # 获取典型值数据 (index 0)
    typ_data = iv_range[0]
    if typ_data is None:
        return [], []
    
    # 解析字符串格式的数据
    data_str = str(typ_data)
    
    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            voltage_str = data_str[start1:end1]
            
            # 找到第二个列表的开始和结束
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            current_str = data_str[start2:end2]
            
            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass
            
            # 解析电流列表
            currents = []
            for i in current_str.split(', '):
                try:
                    currents.append(float(i.strip()))
                except:
                    pass
            
            return voltages, currents
        except Exception as e:
            print(f"Error parsing I-V data: {e}")
            print(f"Data string: {data_str}")
    
    return [], []

def extract_waveform_data(waveform_list):
    """
    从IBIS波形数据中提取时间-电压数据
    返回(time_list, voltage_list)
    """
    if not waveform_list or len(waveform_list) == 0:
        return [], []
    
    # 获取第一个波形数据（典型值）
    waveform = waveform_list[0]
    if 'waveform' not in waveform:
        return [], []
    
    waveform_data = waveform['waveform'][0]  # 获取典型值
    if waveform_data is None:
        return [], []
    
    # 解析字符串格式的数据
    data_str = str(waveform_data)
    
    # 查找包含两个列表的模式: ([...], [...])
    if '([' in data_str and '], [' in data_str:
        try:
            # 找到第一个列表的开始和结束（时间）
            start1 = data_str.find('[') + 1
            end1 = data_str.find('], [')
            time_str = data_str[start1:end1]
            
            # 找到第二个列表的开始和结束（电压）
            start2 = data_str.find('], [') + 4
            end2 = data_str.rfind(']')
            voltage_str = data_str[start2:end2]
            
            # 解析时间列表
            times = []
            for t in time_str.split(', '):
                try:
                    times.append(float(t.strip()))
                except:
                    pass
            
            # 解析电压列表
            voltages = []
            for v in voltage_str.split(', '):
                try:
                    voltages.append(float(v.strip()))
                except:
                    pass
            
            return times, voltages
        except Exception as e:
            print(f"Error parsing waveform data: {e}")
            print(f"Data string: {data_str}")
    
    return [], []

def convert_ibis_to_spice(root, model_name, output_file):
    """
    将IBIS模型转换为SPICE子电路 - 格式修正版本
    """
    if 'Model' not in root or model_name not in root['Model']:
        print(f"Error: Model '{model_name}' not found in IBIS file")
        return False
    
    model = root['Model'][model_name]
    
    # 开始生成SPICE文件
    spice_content = []
    spice_content.append("* SPICE Subcircuit generated from IBIS model")
    spice_content.append(f"* Model: {model_name}")
    spice_content.append("* Generated by ibis_to_spice_converter.py - Format Fixed Version")
    spice_content.append("")
    
    # 硬编码的子电路定义（仍然是问题）
    spice_content.append(f".SUBCKT {model_name} PAD VCC VSS ENABLE PAD_RISE PAD_FALL")
    spice_content.append("* PAD      - I/O pad connection")
    spice_content.append("* VCC      - Power supply")
    spice_content.append("* VSS      - Ground")
    spice_content.append("* ENABLE   - Enable signal (for 3-state models)")
    spice_content.append("* PAD_RISE - Rising waveform reference output")
    spice_content.append("* PAD_FALL - Falling waveform reference output")
    spice_content.append("")
    
    # 添加模型参数
    if 'C_comp' in model:
        c_comp = model['C_comp'][0] if isinstance(model['C_comp'], list) else model['C_comp']
        spice_content.append(f"* Input/Output Capacitance: {format_spice_value(c_comp, 'F')}")
        spice_content.append(f"C_comp PAD VSS {format_spice_value(c_comp, 'F')}")
        spice_content.append("")
    
    # 处理Pulldown特性 - 修正后的TABLE格式
    if 'Pulldown' in model:
        voltages, currents = extract_iv_data(model['Pulldown'])
        if voltages and currents:
            spice_content.append("* Pulldown I-V Characteristic")
            table_line = "Gpd PAD VSS TABLE {V(PAD,VSS)} = "
            
            # 构建TABLE数据点 - 正确的(x,y)格式
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                table_pairs.append(f"({format_spice_value(v)},{format_spice_value(i_val)})")
            
            # 每行最多5个数据对，避免行过长
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")
    
    # 处理Pullup特性 - 修正后的TABLE格式
    if 'Pullup' in model:
        voltages, currents = extract_iv_data(model['Pullup'])
        if voltages and currents:
            spice_content.append("* Pullup I-V Characteristic")
            table_line = "Gpu VCC PAD TABLE {V(VCC,PAD)} = "
            
            # 构建TABLE数据点
            table_pairs = []
            for v, i_val in zip(voltages, currents):
                # 对于pullup，电压相对于VCC，电流取绝对值
                v_rel = 5.0 - v  # 假设VCC=5V
                table_pairs.append(f"({format_spice_value(v_rel)},{format_spice_value(abs(i_val))})")
            
            # 每行最多5个数据对
            pairs_per_line = 5
            for i in range(0, len(table_pairs), pairs_per_line):
                line_pairs = table_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(table_line + " ".join(line_pairs))
                else:
                    spice_content.append("+ " + " ".join(line_pairs))
            spice_content.append("")
    
    # 处理Rising Waveform波形数据 - 修正后的PWL格式
    if 'Rising Waveform' in model:
        times, voltages = extract_waveform_data(model['Rising Waveform'])
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Rising Waveform V-T Characteristic")
            pwl_line = "Vrise_ref PAD_RISE 0 PWL("
            
            # 构建PWL数据点
            pwl_pairs = []
            for t, v in zip(times, voltages):
                pwl_pairs.append(f"{format_spice_value(t, 's')} {format_spice_value(v, 'V')}")
            
            # 每行最多4个时间-电压对
            pairs_per_line = 4
            for i in range(0, len(pwl_pairs), pairs_per_line):
                line_pairs = pwl_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(pwl_line + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
                else:
                    spice_content.append("+ " + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
            spice_content.append("")
    
    # 处理Falling Waveform波形数据 - 修正后的PWL格式
    if 'Falling Waveform' in model:
        times, voltages = extract_waveform_data(model['Falling Waveform'])
        if times and voltages and len(times) == len(voltages):
            spice_content.append("* Falling Waveform V-T Characteristic")
            pwl_line = "Vfall_ref PAD_FALL 0 PWL("
            
            # 构建PWL数据点
            pwl_pairs = []
            for t, v in zip(times, voltages):
                pwl_pairs.append(f"{format_spice_value(t, 's')} {format_spice_value(v, 'V')}")
            
            # 每行最多4个时间-电压对
            pairs_per_line = 4
            for i in range(0, len(pwl_pairs), pairs_per_line):
                line_pairs = pwl_pairs[i:i+pairs_per_line]
                if i == 0:
                    spice_content.append(pwl_line + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
                else:
                    spice_content.append("+ " + " ".join(line_pairs) + (")" if i + pairs_per_line >= len(pwl_pairs) else ""))
            spice_content.append("")
    
    # 结束子电路
    spice_content.append(".ENDS")
    spice_content.append("")
    
    # 写入文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(spice_content))
        print(f"SPICE subcircuit successfully written to {output_file}")
        return True
    except Exception as e:
        print(f"Error writing to file {output_file}: {e}")
        return False

def main():
    """
    主函数 - 格式修正版本
    """
    # 解析IBIS文件
    ibis_file = "ibis/at16245.ibs"
    model_name = "AT16245_OUT"
    output_file = "AT16245_OUTPUT_v2.sp"
    
    print(f"Parsing IBIS file: {ibis_file}")
    
    try:
        parser = IBSParser()
        with open(ibis_file, 'r', encoding='utf-8') as f:
            root = parser.parse(f)
        
        print(f"Successfully parsed IBIS file")
        print(f"Converting model '{model_name}' to SPICE subcircuit...")
        
        # 转换为SPICE
        success = convert_ibis_to_spice(root, model_name, output_file)
        
        if success:
            print(f"Conversion completed successfully!")
            print(f"Output file: {output_file}")
        else:
            print("Conversion failed!")
            
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
