|=============================================================================
| Electrical Board Description
|=============================================================================
| 
[IBIS Ver]      3.2
[File Name]     pinmap.ebd
[File Rev]      1.0
[Date]          April 20, 2003
[Source]        IBIS Open Forum, Data comes from I/O Buffer Information
                Specification Version 3.2
[Disclaimer]    The models given below may not represent any physically
                realizable. They are provided solely for the purpose of
                illustrating the .ebd file format.
|
|=============================================================================
[Begin Board Description]  Board1
[Manufacturer]             IBIS
|
[Number Of Pins] 4
|
[Pin List]  signal_name
 A1          POWER
 A2          GND
 A3          data1
 A4          data2
|
[Path Description] CAS_1
Pin A3
Len = 0.5 L=8.35n C=3.34p R=0.01 /
 Fork
 Len = 1.0 L = 1.0n C= 2.0p /
   Fork
   Len = 0.0 L = 1.0n C= 2.0p /
   Endfork
 Endfork
Len = 0.5 L=8.35n C=3.34p R=0.01 /
Node u21.5
Len = 0.5 L=8.35n C=3.34p R=0.01 /
Pin A4
[Reference Designator Map]     | This keyword is ignored by HSPICE
| Ref Des  File name       Component name
u21        pinmap.ibs          Component1
[End Board Description]        | End: Board1
[End]
