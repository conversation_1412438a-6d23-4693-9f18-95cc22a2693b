*
************** hspice formatted section3 (r,l,g,c elements) *******************
* 4 lines r,l,g,c elements which may be like U element
* for it has the same topology as U element.
* subckt name ==> section numbers + element type + lines number
* .subckt + subckt_name + front-end port pin's list + back-end port pin's list
*******************************************************************************
*
.subckt sect3_rlgc_4 A1 A2 A3 A4 B1 B2 B3 B4 
.param
+ r11=0.357348
+      r22=0.376849
+           r33=0.45466
+                r44=0.356194
+ l11=3.12277e-009 l12=1.01577e-009 l13=3.05886e-010 l14=2.22836e-010
+                  l22=3.02065e-009 l23=4.47214e-010 l24=2.93897e-010
+                                   l33=2.99636e-009 l34=9.45824e-010
+                                                    l44=3.01787e-009
+ c11=4.03601e-013 c12=-1.28769e-013 c13=-1.50353e-014 c14=-8.75617e-015
+                  c22=4.19554e-013  c23=-3.78802e-014 c24=-1.42581e-014
+                                    c33=4.16062e-013  c34=-1.22606e-013
+                                                      c44=4.06634e-013

r_A1_1  A1 1 r='r11/2'
r_A2_4  A2 4 r='r22/2'
r_A3_7  A3 7  r='r33/2'
r_A4_10 A4 10 r='r44/2'

r_B1_3  B1 3 r='r11/2'
r_B2_6  B2 6 r='r22/2'
r_B3_9  B3 9 r='r33/2'
r_B4_12 B4 12 r='r44/2'

l_1_2   1  2 l='l11/2'
l_4_5   4  5 l='l22/2'
l_7_8   7  8 l='l33/2'
l_10_11 10 11 l='l44/2'

l_2_3   2  3 l='l11/2'
l_5_6   5  6 l='l22/2'
l_8_9   8  9 l='l33/2'
l_11_12 11 12 l='l44/2'

k_1_4   l_1_2  l_4_5 k='l12/sqrt(l11*l22)'
k_1_7   l_1_2  l_7_8 k='l13/sqrt(l11*l33)'
k_1_10  l_1_2  l_10_11 k='l14/sqrt(l11*l44)'

k_4_7   l_4_5  l_7_8 k='l23/sqrt(l22*l33)'
k_4_10  l_4_5  l_10_11 k='l24/sqrt(l22*l33)'

k_7_10  l_7_8  l_10_11 k='l34/sqrt(l33*l44)'

k_3_6   l_2_3  l_5_6 k='l12/sqrt(l11*l22)'
k_3_9   l_2_3  l_8_9 k='l13/sqrt(l11*l33)'
k_3_12  l_2_3  l_11_12 k='l14/sqrt(l11*l44)'

k_6_9   l_5_6  l_8_9 k='l23/sqrt(l22*l33)'
k_6_12  l_5_6  l_11_12 k='l24/sqrt(l22*l33)'

k_9_12  l_8_9  l_11_12 k='l34/sqrt(l33*l44)'

c_2_5   2  5 c='-c12'
c_2_8   2  8 c='-c13'
c_2_11  2  11 c='-c14'

c_5_8   5  8 c='-c23'
c_5_11  5  11 c='-c24'

c_8_11  8  11 c='-c34'

c_2_0   2  0  c='c11+c12+c13+c14'
c_5_0   5  0  c='c22+c12+c23+c24'
c_8_0   8  0  c='c33+c13+c23+c34'
c_11_0  11 0  c='c44+c14+c24+c34'
.ends
