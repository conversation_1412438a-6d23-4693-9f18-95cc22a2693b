* SPICE Subcircuit Demo
* Model: INPUT_DIFF_DEMO
* Model Type: Input_diff
* Differential input buffer - LVDS/CML receiver

.SUBCKT INPUT_DIFF_DEMO PAD_P PAD_N VCC VSS
* PAD_P    - Positive differential pad
* PAD_N    - Negative differential pad
* VCC      - Power supply
* VSS      - Ground

* Differential input capacitance
C_comp_p PAD_P VSS 2pF
C_comp_n PAD_N VSS 2pF

* Protection clamps
Gclamp_gnd_p VSS PAD_P TABLE {V(VSS,PAD_P)} = (0.7,0) (0.8,0.01) (0.9,0.1)
Gclamp_gnd_n VSS PAD_N TABLE {V(VSS,PAD_N)} = (0.7,0) (0.8,0.01) (0.9,0.1)
Gclamp_pwr_p PAD_P VCC TABLE {V(PAD_P,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)
Gclamp_pwr_n PAD_N VCC TABLE {V(PAD_N,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)

.ENDS
