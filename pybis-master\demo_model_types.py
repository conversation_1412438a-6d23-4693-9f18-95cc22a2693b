#!/usr/bin/env python3
"""
演示不同IBIS模型类型的转换
创建示例SPICE子电路展示各种模型类型的特点
"""

import os
from ibis_to_spice_converter_v4 import determine_model_features, get_model_type_description

def create_demo_subcircuit(model_type, model_name):
    """
    创建演示用的SPICE子电路定义
    """
    # 获取模型特性
    needs_enable, needs_waveforms, needs_pullup_pulldown, needs_differential = determine_model_features(model_type)
    
    # 生成端口列表
    if needs_differential:
        ports = ["PAD_P", "PAD_N", "VCC", "VSS"]
        port_comments = [
            "* PAD_P    - Positive differential pad",
            "* PAD_N    - Negative differential pad", 
            "* VCC      - Power supply",
            "* VSS      - Ground"
        ]
    else:
        ports = ["PAD", "VCC", "VSS"]
        port_comments = [
            "* PAD      - I/O pad connection",
            "* VCC      - Power supply",
            "* VSS      - Ground"
        ]
    
    if needs_enable:
        ports.append("ENABLE")
        port_comments.append("* ENABLE   - Enable signal for 3-state control")
    
    if needs_waveforms:
        if needs_differential:
            ports.extend(["PAD_P_RISE", "PAD_P_FALL", "PAD_N_RISE", "PAD_N_FALL"])
            port_comments.extend([
                "* PAD_P_RISE - Positive rising waveform reference",
                "* PAD_P_FALL - Positive falling waveform reference",
                "* PAD_N_RISE - Negative rising waveform reference",
                "* PAD_N_FALL - Negative falling waveform reference"
            ])
        else:
            ports.extend(["PAD_RISE", "PAD_FALL"])
            port_comments.extend([
                "* PAD_RISE - Rising waveform reference output",
                "* PAD_FALL - Falling waveform reference output"
            ])
    
    # 生成SPICE内容
    spice_content = []
    spice_content.append("* SPICE Subcircuit Demo")
    spice_content.append(f"* Model: {model_name}")
    spice_content.append(f"* Model Type: {model_type}")
    spice_content.append(f"* {get_model_type_description(model_type)}")
    spice_content.append("")
    
    # 子电路定义
    spice_content.append(f".SUBCKT {model_name} {' '.join(ports)}")
    spice_content.extend(port_comments)
    spice_content.append("")
    
    # 基本电容
    if needs_differential:
        spice_content.append("* Differential input capacitance")
        spice_content.append("C_comp_p PAD_P VSS 2pF")
        spice_content.append("C_comp_n PAD_N VSS 2pF")
    else:
        spice_content.append("* Input/Output capacitance")
        spice_content.append("C_comp PAD VSS 3pF")
    spice_content.append("")
    
    # 根据模型类型添加特定功能
    model_type_norm = model_type.lower().replace('_', '').replace('-', '').replace('/', '')
    
    # Pulldown特性
    if needs_pullup_pulldown and 'opensource' not in model_type_norm:
        spice_content.append("* Pulldown I-V Characteristic")
        if needs_differential:
            spice_content.append("Gpd_p PAD_P VSS TABLE {V(PAD_P,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
            spice_content.append("Gpd_n PAD_N VSS TABLE {V(PAD_N,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
        else:
            spice_content.append("Gpd PAD VSS TABLE {V(PAD,VSS)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
        spice_content.append("")
    
    # Pullup特性
    if needs_pullup_pulldown and 'opendrain' not in model_type_norm and 'opensink' not in model_type_norm:
        spice_content.append("* Pullup I-V Characteristic")
        if needs_differential:
            spice_content.append("Gpu_p VCC PAD_P TABLE {V(VCC,PAD_P)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
            spice_content.append("Gpu_n VCC PAD_N TABLE {V(VCC,PAD_N)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
        else:
            spice_content.append("Gpu VCC PAD TABLE {V(VCC,PAD)} = (0,0) (1,0.1) (2,0.2) (3,0.25) (4,0.28) (5,0.3)")
        spice_content.append("")
    
    # 开漏特殊处理
    if 'opendrain' in model_type_norm or 'opensink' in model_type_norm:
        spice_content.append("* Open drain/sink - only pulldown, no pullup")
        spice_content.append("* External pullup resistor required")
        spice_content.append("")
    
    # 开源特殊处理
    if 'opensource' in model_type_norm:
        spice_content.append("* Open source - only pullup, no pulldown")
        spice_content.append("* External pulldown resistor required")
        spice_content.append("")
    
    # 保护钳位
    spice_content.append("* Protection clamps")
    if needs_differential:
        spice_content.append("Gclamp_gnd_p VSS PAD_P TABLE {V(VSS,PAD_P)} = (0.7,0) (0.8,0.01) (0.9,0.1)")
        spice_content.append("Gclamp_gnd_n VSS PAD_N TABLE {V(VSS,PAD_N)} = (0.7,0) (0.8,0.01) (0.9,0.1)")
        spice_content.append("Gclamp_pwr_p PAD_P VCC TABLE {V(PAD_P,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)")
        spice_content.append("Gclamp_pwr_n PAD_N VCC TABLE {V(PAD_N,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)")
    else:
        spice_content.append("Gclamp_gnd VSS PAD TABLE {V(VSS,PAD)} = (0.7,0) (0.8,0.01) (0.9,0.1)")
        spice_content.append("Gclamp_pwr PAD VCC TABLE {V(PAD,VCC)} = (5.7,0) (5.8,0.01) (5.9,0.1)")
    spice_content.append("")
    
    # 波形数据
    if needs_waveforms:
        spice_content.append("* Timing waveforms")
        if needs_differential:
            spice_content.append("Vrise_p PAD_P_RISE 0 PWL(0 0V 1ns 2.5V 2ns 5V)")
            spice_content.append("Vfall_p PAD_P_FALL 0 PWL(0 5V 1ns 2.5V 2ns 0V)")
            spice_content.append("Vrise_n PAD_N_RISE 0 PWL(0 5V 1ns 2.5V 2ns 0V)")
            spice_content.append("Vfall_n PAD_N_FALL 0 PWL(0 0V 1ns 2.5V 2ns 5V)")
        else:
            spice_content.append("Vrise PAD_RISE 0 PWL(0 0V 1ns 2.5V 2ns 5V)")
            spice_content.append("Vfall PAD_FALL 0 PWL(0 5V 1ns 2.5V 2ns 0V)")
        spice_content.append("")
    
    # 特殊模型类型的额外注释
    if model_type == 'Terminator':
        spice_content.append("* Passive termination - no active drive")
        spice_content.append("Rterm PAD VSS 50")
        spice_content.append("")
    elif 'series' in model_type_norm:
        spice_content.append("* Series termination resistance")
        spice_content.append("Rseries PAD PAD_INT 25")
        spice_content.append("")
    elif 'ecl' in model_type_norm:
        spice_content.append("* ECL logic levels: VIH=-0.8V, VIL=-1.6V")
        spice_content.append("* ECL supply: VCC=0V, VEE=-5.2V")
        spice_content.append("")
    
    spice_content.append(".ENDS")
    spice_content.append("")
    
    return '\n'.join(spice_content)

def generate_all_demo_models():
    """
    生成所有模型类型的演示文件
    """
    # 选择代表性的模型类型进行演示
    demo_models = [
        ('Input', 'INPUT_DEMO'),
        ('Output', 'OUTPUT_DEMO'),
        ('3-state', 'TRISTATE_DEMO'),
        ('I/O', 'IO_DEMO'),
        ('Open_drain', 'OPENDRAIN_DEMO'),
        ('I/O_open_drain', 'IO_OPENDRAIN_DEMO'),
        ('Input_diff', 'INPUT_DIFF_DEMO'),
        ('Output_diff', 'OUTPUT_DIFF_DEMO'),
        ('3-state_diff', 'TRISTATE_DIFF_DEMO'),
        ('Series', 'SERIES_DEMO'),
        ('Terminator', 'TERMINATOR_DEMO')
    ]
    
    # 创建演示目录
    demo_dir = "model_type_demos"
    if not os.path.exists(demo_dir):
        os.makedirs(demo_dir)
    
    print("Generating SPICE subcircuit demos for all model types...")
    print("=" * 60)
    
    for model_type, model_name in demo_models:
        # 生成SPICE内容
        spice_content = create_demo_subcircuit(model_type, model_name)
        
        # 写入文件
        filename = f"{demo_dir}/{model_name}.sp"
        with open(filename, 'w') as f:
            f.write(spice_content)
        
        # 获取特性信息
        enable, waveforms, pullup_pulldown, differential = determine_model_features(model_type)
        
        print(f"✅ {model_type:<15} -> {filename}")
        print(f"   Features: Enable={enable}, Waveforms={waveforms}, Pull={pullup_pulldown}, Diff={differential}")
        
        # 显示端口信息
        lines = spice_content.split('\n')
        subckt_line = next((line for line in lines if line.startswith('.SUBCKT')), '')
        if subckt_line:
            ports = subckt_line.split()[2:]  # 跳过.SUBCKT和模型名
            print(f"   Ports ({len(ports)}): {' '.join(ports)}")
        print()
    
    print(f"All demo files generated in '{demo_dir}/' directory")
    
    # 生成总结文件
    summary_file = f"{demo_dir}/README.md"
    with open(summary_file, 'w') as f:
        f.write("# IBIS Model Type Demos\n\n")
        f.write("This directory contains SPICE subcircuit demonstrations for various IBIS model types.\n\n")
        f.write("## Generated Files\n\n")
        
        for model_type, model_name in demo_models:
            enable, waveforms, pullup_pulldown, differential = determine_model_features(model_type)
            description = get_model_type_description(model_type)
            
            f.write(f"### {model_name}.sp\n")
            f.write(f"- **Type**: {model_type}\n")
            f.write(f"- **Description**: {description}\n")
            f.write(f"- **Features**: Enable={enable}, Waveforms={waveforms}, Pullup/Pulldown={pullup_pulldown}, Differential={differential}\n\n")
        
        f.write("## Usage\n\n")
        f.write("Include these subcircuits in your SPICE simulations:\n\n")
        f.write("```spice\n")
        f.write(".include INPUT_DEMO.sp\n")
        f.write("X1 signal vcc vss INPUT_DEMO\n")
        f.write("```\n\n")
        f.write("Each subcircuit demonstrates the typical structure and components for that model type.\n")
    
    print(f"Summary documentation: {summary_file}")

def main():
    """主函数"""
    print("IBIS Model Type Demo Generator")
    print("=" * 60)
    print()
    
    generate_all_demo_models()

if __name__ == "__main__":
    main()
